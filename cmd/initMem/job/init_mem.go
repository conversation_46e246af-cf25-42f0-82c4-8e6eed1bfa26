package job

import (
	"context"
	"github.com/reugn/go-quartz/quartz"
	"log"
	"wego-common-tmp/internal/cache/appRouterInfoCache"
	"wego-common-tmp/internal/cache/crowdPack"
	"wego-common-tmp/internal/cache/resourceLocationCache"
)

// PrintJob implements the quartz.Job interface.
type InitMem struct {
	Desc  string
	Topic string
}

// Description returns the description of the PrintJob.
func (pj *InitMem) Description() string {
	return pj.Desc
}

// Key returns the unique PrintJob key.
func (pj *InitMem) Key() int {
	return quartz.HashCode(pj.Description())
}

// Execute is called by a Scheduler when the Trigger associated with this job fires.
func (pj *InitMem) Execute() {
	log.Println("定时同步Redis数据到内存")
	ctx := context.Background()
	resourceLocationCache.ResourceLocationService.InitResouceLocationCache(ctx)
	crowdPack.CrowdPackService.InitCrowdPackCache(ctx)
	appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
	log.Println("定时同步Redis数据到内存结束")
}
