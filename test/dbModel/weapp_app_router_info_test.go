package dbModel

import (
	"testing"
	"time"
	"wego-common-tmp/internal/model/dbModel"
	"wego-common-tmp/test"
)

func TestWeappAppRouterInfoModel(t *testing.T) {
	// 初始化配置
	test.Configinit()

	t.Run("测试创建app路由信息", func(t *testing.T) {
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 1,
			Router:              "/pages/test?id=123",
			Creator:             1001,
			Updater:             1001,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建app路由信息失败: %v", err)
			return
		}

		t.Logf("创建成功，ID: %d", routerInfo.Id)

		// 验证创建的数据
		if routerInfo.Id == 0 {
			t.Error("创建后ID应该不为0")
		}
		if routerInfo.State != dbModel.APP_ROUTER_STATE_NORMAL {
			t.Error("创建后状态应该为正常")
		}
	})

	t.Run("测试根据ID获取app路由信息", func(t *testing.T) {
		// 先创建一个测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 2,
			Router:              "/pages/detail?id=456",
			Creator:             1002,
			Updater:             1002,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 根据ID获取
		result, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByID(routerInfo.Id)
		if err != nil {
			t.Logf("根据ID获取app路由信息失败: %v", err)
			return
		}

		if result == nil {
			t.Error("获取结果不应该为空")
			return
		}

		if result.Router != routerInfo.Router {
			t.Errorf("路由信息不匹配，期望: %s, 实际: %s", routerInfo.Router, result.Router)
		}

		t.Logf("获取成功: ID=%d, Router=%s", result.Id, result.Router)
	})

	t.Run("测试根据模板ID获取app路由列表", func(t *testing.T) {
		templateId := 3

		// 创建多个测试数据
		testRouters := []string{
			"/pages/list?type=1",
			"/pages/list?type=2",
			"/pages/list?type=3",
		}

		for i, router := range testRouters {
			routerInfo := &dbModel.WeappAppRouterInfo{
				AppRouterTemplateId: templateId,
				Router:              router,
				Creator:             1003 + i,
				Updater:             1003 + i,
			}

			err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
			if err != nil {
				t.Logf("创建测试数据失败: %v", err)
				continue
			}
		}

		// 根据模板ID获取列表
		results, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfosByTemplateID(templateId)
		if err != nil {
			t.Logf("根据模板ID获取app路由列表失败: %v", err)
			return
		}

		t.Logf("获取到 %d 个路由信息", len(results))

		for i, result := range results {
			if result.AppRouterTemplateId != templateId {
				t.Errorf("模板ID不匹配，期望: %d, 实际: %d", templateId, result.AppRouterTemplateId)
			}
			t.Logf("路由 %d: ID=%d, Router=%s", i+1, result.Id, result.Router)
		}
	})

	t.Run("测试根据模板ID获取最新一条app路由信息", func(t *testing.T) {
		templateId := 10

		// 创建多个测试数据，模拟不同时间创建
		testRouters := []string{
			"/pages/latest/first",
			"/pages/latest/second",
			"/pages/latest/third",  // 这个应该是最新的
		}

		var createdIds []int
		for i, router := range testRouters {
			routerInfo := &dbModel.WeappAppRouterInfo{
				AppRouterTemplateId: templateId,
				Router:              router,
				Creator:             2000 + i,
				Updater:             2000 + i,
			}

			err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
			if err != nil {
				t.Logf("创建测试数据失败: %v", err)
				continue
			}
			createdIds = append(createdIds, routerInfo.Id)

			// 稍微延迟，确保创建时间不同
			time.Sleep(10 * time.Millisecond)
		}

		if len(createdIds) == 0 {
			t.Log("没有成功创建测试数据，跳过测试")
			return
		}

		// 获取最新的一条记录
		latest, err := dbModel.WeappAppRouterInfoModelService.GetLatestAppRouterInfoByTemplateID(templateId)
		if err != nil {
			t.Logf("根据模板ID获取最新app路由信息失败: %v", err)
			return
		}

		if latest == nil {
			t.Error("获取结果不应该为空")
			return
		}

		// 验证获取到的是最新的记录（应该是最后创建的）
		expectedRouter := "/pages/latest/third"
		if latest.Router != expectedRouter {
			t.Errorf("获取的不是最新记录，期望: %s, 实际: %s", expectedRouter, latest.Router)
		}

		if latest.AppRouterTemplateId != templateId {
			t.Errorf("模板ID不匹配，期望: %d, 实际: %d", templateId, latest.AppRouterTemplateId)
		}

		t.Logf("获取最新记录成功: ID=%d, Router=%s, CreatedAt=%s",
			latest.Id, latest.Router, latest.CreatedAt.Format("2006-01-02 15:04:05"))
	})

	t.Run("测试根据路由字符串获取路由信息", func(t *testing.T) {
		router := "/pages/unique?timestamp=" + time.Now().Format("20060102150405")

		// 创建测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 4,
			Router:              router,
			Creator:             1004,
			Updater:             1004,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 根据路由字符串获取
		result, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByRouter(router)
		if err != nil {
			t.Logf("根据路由字符串获取路由信息失败: %v", err)
			return
		}

		if result == nil {
			t.Error("获取结果不应该为空")
			return
		}

		if result.Router != router {
			t.Errorf("路由信息不匹配，期望: %s, 实际: %s", router, result.Router)
		}

		t.Logf("获取成功: ID=%d, Router=%s", result.Id, result.Router)
	})

	t.Run("测试更新app路由信息", func(t *testing.T) {
		// 先创建一个测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 5,
			Router:              "/pages/update/before",
			Creator:             1005,
			Updater:             1005,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 更新数据
		routerInfo.Router = "/pages/update/after"
		routerInfo.Updater = 2005

		err = dbModel.WeappAppRouterInfoModelService.UpdateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("更新app路由信息失败: %v", err)
			return
		}

		// 验证更新结果
		result, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByID(routerInfo.Id)
		if err != nil {
			t.Logf("获取更新后的数据失败: %v", err)
			return
		}

		if result.Router != "/pages/update/after" {
			t.Errorf("更新失败，期望: %s, 实际: %s", "/pages/update/after", result.Router)
		}

		if result.Updater != 2005 {
			t.Errorf("更新人不匹配，期望: %d, 实际: %d", 2005, result.Updater)
		}

		t.Logf("更新成功: Router=%s, Updater=%d", result.Router, result.Updater)
	})

	t.Run("测试软删除app路由信息", func(t *testing.T) {
		// 先创建一个测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 6,
			Router:              "/pages/delete/test",
			Creator:             1006,
			Updater:             1006,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 软删除
		err = dbModel.WeappAppRouterInfoModelService.DeleteAppRouterInfo(routerInfo.Id, 2006)
		if err != nil {
			t.Logf("软删除app路由信息失败: %v", err)
			return
		}

		// 验证删除结果（应该获取不到）
		result, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByID(routerInfo.Id)
		if err == nil && result != nil {
			t.Error("软删除后应该获取不到数据")
		}

		t.Logf("软删除成功，ID: %d", routerInfo.Id)
	})

	t.Run("测试获取所有正常状态的app路由信息", func(t *testing.T) {
		results, err := dbModel.WeappAppRouterInfoModelService.GetAllAppRouterInfos()
		if err != nil {
			t.Logf("获取所有app路由信息失败: %v", err)
			return
		}

		t.Logf("获取到 %d 个正常状态的路由信息", len(results))

		// 验证所有结果的状态都是正常的
		for i, result := range results {
			if result.State != dbModel.APP_ROUTER_STATE_NORMAL {
				t.Errorf("第 %d 个结果状态不正常: %d", i+1, result.State)
			}
			if i < 5 { // 只打印前5个
				t.Logf("路由 %d: ID=%d, Router=%s, TemplateID=%d", i+1, result.Id, result.Router, result.AppRouterTemplateId)
			}
		}
	})
}
