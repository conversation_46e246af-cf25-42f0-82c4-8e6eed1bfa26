package cache

import (
	"context"
	"testing"
	"time"
	"wego-common-tmp/internal/cache/appRouterInfoCache"
	"wego-common-tmp/internal/model/dbModel"
	"wego-common-tmp/test"
)

func TestAppRouterInfoCache(t *testing.T) {
	// 初始化配置
	test.Configinit()

	t.Run("测试本地缓存覆盖方式初始化", func(t *testing.T) {
		ctx := context.Background()

		// 先创建一些测试数据
		testData := []*dbModel.WeappAppRouterInfo{
			{
				AppRouterTemplateId: 100,
				Router:              "/pages/test/cache1",
				Creator:             1001,
				Updater:             1001,
			},
			{
				AppRouterTemplateId: 100,
				Router:              "/pages/test/cache2",
				Creator:             1002,
				Updater:             1002,
			},
			{
				AppRouterTemplateId: 101,
				Router:              "/pages/test/cache3",
				Creator:             1003,
				Updater:             1003,
			},
		}

		var createdIds []int
		for i, data := range testData {
			err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(data)
			if err != nil {
				t.Logf("创建测试数据 %d 失败: %v", i+1, err)
				continue
			}
			createdIds = append(createdIds, data.Id)

			// 稍微延迟，确保创建时间不同
			time.Sleep(10 * time.Millisecond)
		}

		if len(createdIds) == 0 {
			t.Log("没有成功创建测试数据，跳过测试")
			return
		}

		// 先手动设置一些旧的缓存数据，验证覆盖效果
		oldCacheKey := "app_router_info:id:999999"
		oldData := &dbModel.WeappAppRouterInfo{Id: 999999, Router: "/old/cache/data"}
		// 模拟设置旧缓存（这里直接操作本地缓存来模拟）

		// 执行初始化缓存（采用覆盖方式）
		err := appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
		if err != nil {
			t.Fatalf("初始化缓存失败: %v", err)
		}

		t.Log("缓存初始化成功（采用覆盖方式）")

		// 验证单个路由信息缓存
		for _, id := range createdIds {
			routerInfo, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, id)
			if err != nil {
				t.Errorf("获取路由信息失败 ID=%d: %v", id, err)
				continue
			}
			if routerInfo == nil {
				t.Errorf("路由信息不应该为空 ID=%d", id)
				continue
			}
			t.Logf("缓存验证成功: ID=%d, Router=%s", routerInfo.Id, routerInfo.Router)
		}

		// 验证根据模板ID获取最新路由信息
		latestRouter100, err := appRouterInfoCache.AppRouterInfoService.GetLatestAppRouterInfoByTemplateID(ctx, 100)
		if err != nil {
			t.Errorf("获取最新路由信息失败 TemplateID=100: %v", err)
		} else if latestRouter100 != nil {
			t.Logf("最新路由信息: TemplateID=100, Router=%s, CreatedAt=%s",
				latestRouter100.Router, latestRouter100.CreatedAt.Format("2006-01-02 15:04:05"))
		}

		// 验证根据模板ID获取路由列表
		routerList100, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfosByTemplateID(ctx, 100)
		if err != nil {
			t.Errorf("获取路由列表失败 TemplateID=100: %v", err)
		} else {
			t.Logf("路由列表: TemplateID=100, 数量=%d", len(routerList100))
		}

		// 验证根据路由字符串获取路由信息
		routerByString, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByRouter(ctx, "/pages/test/cache1")
		if err != nil {
			t.Errorf("根据路由字符串获取路由信息失败: %v", err)
		} else if routerByString != nil {
			t.Logf("根据路由字符串获取成功: ID=%d, Router=%s", routerByString.Id, routerByString.Router)
		}
	})

	t.Run("测试缓存刷新功能", func(t *testing.T) {
		ctx := context.Background()

		// 创建测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 200,
			Router:              "/pages/test/refresh",
			Creator:             2001,
			Updater:             2001,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 先获取一次，确保数据在缓存中
		cached, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, routerInfo.Id)
		if err != nil {
			t.Logf("获取缓存数据失败: %v", err)
			return
		}
		if cached == nil {
			t.Log("缓存数据为空")
			return
		}

		t.Logf("缓存数据获取成功: ID=%d", cached.Id)

		// 刷新单个缓存
		err = appRouterInfoCache.AppRouterInfoService.RefreshAppRouterInfoCache(ctx, routerInfo.Id)
		if err != nil {
			t.Errorf("刷新单个缓存失败: %v", err)
		} else {
			t.Log("刷新单个缓存成功")
		}

		// 刷新模板ID相关缓存
		err = appRouterInfoCache.AppRouterInfoService.RefreshLatestAppRouterInfoByTemplateIDCache(ctx, 200)
		if err != nil {
			t.Errorf("刷新最新路由缓存失败: %v", err)
		} else {
			t.Log("刷新最新路由缓存成功")
		}

		err = appRouterInfoCache.AppRouterInfoService.RefreshAppRouterInfosByTemplateIDCache(ctx, 200)
		if err != nil {
			t.Errorf("刷新路由列表缓存失败: %v", err)
		} else {
			t.Log("刷新路由列表缓存成功")
		}
	})

	t.Run("测试缓存性能", func(t *testing.T) {
		ctx := context.Background()

		// 创建测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 300,
			Router:              "/pages/test/performance",
			Creator:             3001,
			Updater:             3001,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 初始化缓存
		err = appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
		if err != nil {
			t.Logf("初始化缓存失败: %v", err)
			return
		}

		// 测试多次获取的性能
		iterations := 100
		start := time.Now()

		for i := 0; i < iterations; i++ {
			_, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, routerInfo.Id)
			if err != nil {
				t.Errorf("第 %d 次获取失败: %v", i+1, err)
				break
			}
		}

		duration := time.Since(start)
		avgTime := duration / time.Duration(iterations)

		t.Logf("性能测试完成: %d 次获取耗时 %v, 平均每次 %v", iterations, duration, avgTime)

		if avgTime > 10*time.Millisecond {
			t.Logf("警告: 平均响应时间较长 %v", avgTime)
		}
	})

	t.Run("测试缓存覆盖功能", func(t *testing.T) {
		ctx := context.Background()

		// 创建测试数据
		routerInfo := &dbModel.WeappAppRouterInfo{
			AppRouterTemplateId: 400,
			Router:              "/pages/test/override",
			Creator:             4001,
			Updater:             4001,
		}

		err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("创建测试数据失败: %v", err)
			return
		}

		// 第一次初始化缓存
		err = appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
		if err != nil {
			t.Logf("第一次初始化缓存失败: %v", err)
			return
		}

		// 获取缓存数据
		cached1, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, routerInfo.Id)
		if err != nil || cached1 == nil {
			t.Logf("第一次获取缓存数据失败: %v", err)
			return
		}

		t.Logf("第一次缓存数据: ID=%d, Router=%s", cached1.Id, cached1.Router)

		// 更新数据库中的数据
		routerInfo.Router = "/pages/test/override/updated"
		err = dbModel.WeappAppRouterInfoModelService.UpdateAppRouterInfo(routerInfo)
		if err != nil {
			t.Logf("更新数据库数据失败: %v", err)
			return
		}

		// 第二次初始化缓存（覆盖方式）
		err = appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
		if err != nil {
			t.Logf("第二次初始化缓存失败: %v", err)
			return
		}

		// 再次获取缓存数据，验证是否已更新
		cached2, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, routerInfo.Id)
		if err != nil || cached2 == nil {
			t.Logf("第二次获取缓存数据失败: %v", err)
			return
		}

		t.Logf("第二次缓存数据: ID=%d, Router=%s", cached2.Id, cached2.Router)

		// 验证缓存已被覆盖更新
		if cached2.Router != "/pages/test/override/updated" {
			t.Errorf("缓存覆盖失败，期望: %s, 实际: %s", "/pages/test/override/updated", cached2.Router)
		} else {
			t.Log("缓存覆盖功能验证成功")
		}

		t.Log("缓存覆盖测试完成")
	})
}
