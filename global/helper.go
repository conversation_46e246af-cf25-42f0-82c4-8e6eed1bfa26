package global

import (
	"github.com/bytedance/sonic"
	"strconv"
)

// JsonStrToIntSlice 将 JSON 字符串转换为整数切片
func JsonStrToIntSlice(jsonStr string) ([]int, error) {
	var strSlice []string
	// 解析 JSON 字符串到字符串切片
	err := sonic.Unmarshal([]byte(jsonStr), &strSlice)
	if err != nil {
		return nil, err
	}

	intSlice := make([]int, len(strSlice))
	for i, str := range strSlice {
		num, err := strconv.Atoi(str)
		if err != nil {
			return nil, err
		}
		intSlice[i] = num
	}
	return intSlice, nil
}
