package global

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/alarm"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/mq/rabbitmq"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
)

type AppGlobalConfig struct {
	ServerConfig        *app.ServerConfig                 `yaml:"server"`
	LogConfig           *log.Config                       `yaml:"log"`
	MysqlDbConfig       *mysql.MysqlDbConfig              `yaml:"mysqlDbConfig"`
	RedisConfig         *redisManager.RedisConfig         `yaml:"redis"`
	AlarmConfig         *alarm.AlarmConfig                `yaml:"alarmConfig"`
	RabbitMqConfig      *rabbitmq.MQConfig                `yaml:"rabbitmq"`
	LimiterConfig       *utils.LimiterConfig              `yaml:"limiter"`
	RemoteServiceConfig *remote.RemoteServiceGlobalConfig `yaml:"remoteServiceConfig"`
	BaseConfig          *app.Baseconfig                   `yaml:"baseConfig"`
	HealthCheckConfig   *middleware.HealthCheckConfig     `yaml:"healthCheckConfig"`
}

var AppConfig AppGlobalConfig

var DefaultExchange = "wego_common"
