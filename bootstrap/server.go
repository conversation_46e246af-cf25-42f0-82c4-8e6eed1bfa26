package bootstrap

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/alarm"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/gocache"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"github.com/gin-gonic/gin"
	"wego-common-tmp/global"
)

func RunServer(r *gin.Engine) {
	config := global.AppConfig.ServerConfig
	var server = app.Server{
		Config: config,
		Engine: r,
	}
	InitPkg(&global.AppConfig)
	server.NewServer(config).Start()
}

// 初始化基础组件
func InitPkg(config *global.AppGlobalConfig) {
	// 初始化各个基础组件
	log.InitLog(config.LogConfig)
	mysql.InitMysqlDb(config.MysqlDbConfig)
	redisManager.InitRedis(config.RedisConfig)
	alarm.InitAlarmServive(config.AlarmConfig)
	InitMqManager(config.RabbitMqConfig)
	InitRemoteService(config.RemoteServiceConfig)
	middleware.InitHealthCheckManager(config.HealthCheckConfig)
	gocache.Init()
}
