package bootstrap

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
	"wego-common-tmp/global"
	v1 "wego-common-tmp/internal/api/v1"
	"wego-common-tmp/internal/api/v1/resouceLocation"
)

func InitRouterGroup() *gin.Engine {
	r := gin.New()

	manager := utils.InitLimiter(global.AppConfig.LimiterConfig.GlobalQPS)
	cbManager := utils.InitBreaker()

	RegisterAllRouters(r, manager, cbManager)
	InitOtherRouterRegister(cbManager, r)

	return r
}

// RegisterAllRouters 注册所有路由组
func RegisterAllRouters(r *gin.Engine, manager *utils.RouteLimiterManager, cbManager *utils.CircuitBreakerManager) {
	v1.RegisterUserRouter(r, manager, cbManager)
	resouceLocation.RegisterResourceLocationRouter(r, manager, cbManager)
	resouceLocation.RegisterResourceLocationOpenApiRouter(r, manager, cbManager)
}

// 限流熔断操作初始化
func InitOtherRouterRegister(cbManager *utils.CircuitBreakerManager, r *gin.Engine) {
	cbManager.CreateCircuitBreakers(r)
}
