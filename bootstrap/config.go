package bootstrap

import (
	"github.com/jinzhu/configor"
	"github.com/spf13/cobra"
	"os"
	"wego-common-tmp/global"
)

func InitConfig() {
	var greetCmd = &cobra.Command{}

	var configPath string

	greetCmd.Flags().StringVarP(&configPath, "config", "c", "./config/settings.yml", "path to the config file")

	if err := greetCmd.Execute(); err != nil {
		os.Exit(1)
	}

	configor.New(&configor.Config{Debug: true}).Load(&global.AppConfig, configPath)

}
