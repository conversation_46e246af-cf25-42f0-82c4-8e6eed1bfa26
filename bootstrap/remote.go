package bootstrap

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/datacenter"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/javacenter"
	"time"
)

// InitRemoteService 初始化远程服务
func InitRemoteService(config *remote.RemoteServiceGlobalConfig) *base.RemoteServiceManager {
	manager := base.InitRemoteServiceManager()

	userCenterService := datacenter.NewUserCenterService(&datacenter.ServiceConfig{
		BaseURL:   config.DataCenterConfig.BaseURL,
		AppID:     config.DataCenterConfig.AppID,
		AppSecret: config.DataCenterConfig.AppSecret,
	})
	manager.RegisterRemoteService(consts.UserCenterService, userCenterService, func(config *base.RemoteServiceConfig) { config.Timeout = 3 * time.Second })

	tagCenterService := javacenter.NewJavaCenterService(&javacenter.ServiceConfig{
		BaseURL:   config.JavaCenterConfig.BaseURL,
		AppID:     config.JavaCenterConfig.AppID,
		AppSecret: config.JavaCenterConfig.AppSecret,
	})

	manager.RegisterRemoteService(consts.JavaCenterService, tagCenterService, func(config *base.RemoteServiceConfig) { config.Timeout = 3 * time.Second })
	return manager
}
