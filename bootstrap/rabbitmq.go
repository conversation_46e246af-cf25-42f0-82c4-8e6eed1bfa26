package bootstrap

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/mq/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/mq/rabbitmq"
	"log"
	"wego-common-tmp/internal/mq"
)

var RabbitMqManager base.IManager

func InitMqManager(config *rabbitmq.MQConfig) {
	log.Println("初始化rabbitmq")
	RabbitMqManager = rabbitmq.NewRabbitMQManager(config)
	err := mq.Init(RabbitMqManager)
	if err != nil {
		log.Fatal(err)
	}
	RabbitMqManager.Start(context.Background())
	log.Println("rabbitmq初始化完成")
}
