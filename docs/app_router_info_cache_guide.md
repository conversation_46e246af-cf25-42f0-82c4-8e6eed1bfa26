# App路由信息缓存系统使用指南

## 概述

App路由信息缓存系统提供了完整的二级缓存解决方案，包括本地缓存和Redis缓存，用于提升路由信息查询的性能。

## 缓存架构

### 二级缓存结构

```
┌─────────────────┐
│   API Layer     │  ← 业务接口层
├─────────────────┤
│ Local Cache     │  ← L1缓存（本地内存）
├─────────────────┤
│ Redis Cache     │  ← L2缓存（Redis）
├─────────────────┤
│ Database        │  ← 数据库层
└─────────────────┘
```

### 缓存策略

- **L1缓存（本地）**：无过期时间，进程内存储，响应最快
- **L2缓存（Redis）**：有过期时间，跨进程共享，保证数据一致性
- **缓存穿透保护**：空结果也进行缓存
- **缓存更新**：支持手动刷新和自动过期

## 核心功能

### 1. 缓存查询方法

#### GetAppRouterInfoByID
根据ID获取单个app路由信息（带缓存）

```go
routerInfo, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, id)
```

#### GetLatestAppRouterInfoByTemplateID
根据模板ID获取最新app路由信息（带缓存）

```go
latestRouter, err := appRouterInfoCache.AppRouterInfoService.GetLatestAppRouterInfoByTemplateID(ctx, templateId)
```

#### GetAppRouterInfosByTemplateID
根据模板ID获取app路由列表（带缓存）

```go
routerList, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfosByTemplateID(ctx, templateId)
```

#### GetAppRouterInfoByRouter
根据路由字符串获取路由信息（带缓存）

```go
routerInfo, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByRouter(ctx, router)
```

### 2. 缓存管理方法

#### InitAllAppRouterInfoCache
初始化所有路由信息缓存，采用本地缓存覆盖的方式重建缓存

```go
err := appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
```

**功能说明：**
1. 从数据库获取所有正常状态的路由信息
2. 按模板ID分组处理数据
3. 覆盖单个路由信息缓存（本地+Redis）
4. 覆盖路由列表缓存（本地+Redis）
5. 覆盖最新路由信息缓存（本地+Redis）
6. 覆盖路由字符串映射缓存（本地+Redis）

**覆盖策略：**
- 不清空现有缓存，直接覆盖相关键值
- 保证缓存的连续性，避免缓存空窗期
- 提高初始化性能，减少缓存重建时间

#### RefreshAppRouterInfoCache
刷新单个app路由信息缓存

```go
err := appRouterInfoCache.AppRouterInfoService.RefreshAppRouterInfoCache(ctx, id)
```

#### RefreshLatestAppRouterInfoByTemplateIDCache
刷新根据模板ID获取最新路由信息的缓存

```go
err := appRouterInfoCache.AppRouterInfoService.RefreshLatestAppRouterInfoByTemplateIDCache(ctx, templateId)
```

#### RefreshAppRouterInfosByTemplateIDCache
刷新根据模板ID获取路由列表的缓存

```go
err := appRouterInfoCache.AppRouterInfoService.RefreshAppRouterInfosByTemplateIDCache(ctx, templateId)
```

## API接口

### 1. 初始化缓存接口

**接口地址：** `POST /v1/appRouterInfoCache/init`

**功能：** 初始化所有app路由信息缓存

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "message": "app路由信息缓存初始化成功",
        "time": "2024-01-01T12:00:00Z"
    }
}
```

### 2. 刷新缓存接口

**接口地址：** `POST /v1/appRouterInfoCache/refresh`

**功能：** 刷新指定app路由信息缓存

**请求参数：**
```json
{
    "type": 1,          // 刷新类型：1-单个ID，2-模板ID最新，3-模板ID列表
    "id": 123,          // 路由信息ID（type=1时必填）
    "template_id": 456  // 模板ID（type=2或3时必填）
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "message": "单个路由信息缓存刷新成功",
        "type": 1,
        "id": 123,
        "template_id": 0,
        "time": "2024-01-01T12:00:00Z"
    }
}
```

## 缓存键规则

### 缓存键格式

- **单个路由信息：** `app_router_info:id:{id}`
- **最新路由信息：** `app_router_info:latest:template_id:{template_id}`
- **路由列表：** `app_router_info:list:template_id:{template_id}`
- **路由字符串映射：** `app_router_info:router:{router}`

### 过期时间设置

- **单个路由信息：** 3600秒（1小时）
- **最新路由信息：** 1800秒（30分钟）
- **路由列表：** 3600秒（1小时）
- **路由字符串映射：** 3600秒（1小时）

## 使用场景

### 1. 系统启动时初始化

```go
// 在系统启动时调用
func init() {
    ctx := context.Background()
    err := appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
    if err != nil {
        log.Fatal("初始化路由缓存失败", err)
    }
}
```

### 2. 数据更新后刷新缓存

```go
// 创建新路由后刷新相关缓存
func createRouter(templateId int, router string) error {
    // 创建路由信息
    routerInfo := &dbModel.WeappAppRouterInfo{
        AppRouterTemplateId: templateId,
        Router:              router,
    }
    err := dbModel.WeappAppRouterInfoModelService.CreateAppRouterInfo(routerInfo)
    if err != nil {
        return err
    }
    
    // 刷新相关缓存
    ctx := context.Background()
    appRouterInfoCache.AppRouterInfoService.RefreshLatestAppRouterInfoByTemplateIDCache(ctx, templateId)
    appRouterInfoCache.AppRouterInfoService.RefreshAppRouterInfosByTemplateIDCache(ctx, templateId)
    
    return nil
}
```

### 3. 定时任务刷新

```go
// 定时刷新缓存
func scheduleRefreshCache() {
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            ctx := context.Background()
            err := appRouterInfoCache.AppRouterInfoService.InitAllAppRouterInfoCache(ctx)
            if err != nil {
                log.Error("定时刷新缓存失败", err)
            }
        }
    }
}
```

## 性能优化

### 1. 缓存命中率监控

建议监控以下指标：
- 本地缓存命中率
- Redis缓存命中率
- 数据库查询次数
- 平均响应时间

### 2. 内存使用优化

- 本地缓存使用LRU策略
- 设置合理的缓存大小限制
- 定期清理过期数据

### 3. 网络优化

- Redis连接池配置
- 批量操作减少网络往返
- 数据压缩传输

## 故障处理

### 1. 缓存失效

**现象：** 缓存数据不一致或过期

**解决方案：**
```bash
# 调用初始化接口重建缓存
curl -X POST http://localhost:8080/v1/appRouterInfoCache/init
```

### 2. Redis连接失败

**现象：** Redis不可用，降级到数据库查询

**解决方案：**
- 检查Redis服务状态
- 验证连接配置
- 重启应用服务

### 3. 内存占用过高

**现象：** 本地缓存占用内存过多

**解决方案：**
- 调整缓存大小限制
- 清理本地缓存
- 重启应用释放内存

## 最佳实践

1. **合理设置过期时间**：根据数据更新频率设置
2. **及时刷新缓存**：数据变更后立即刷新相关缓存
3. **监控缓存性能**：定期检查命中率和响应时间
4. **错误处理**：缓存失败时优雅降级到数据库查询
5. **批量操作**：避免频繁的单个缓存操作
