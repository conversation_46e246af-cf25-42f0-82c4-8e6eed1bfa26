package v1

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/alarm"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
	"log"
)

func RegisterKeepAliveRouter(r *gin.Engine, manager *utils.RouteLimiterManager, cbManager *utils.CircuitBreakerManager) {
	// 注册中间件
	r.Use(
		middleware.Cors(),
		middleware.GinLogger(),
		middleware.Recover(),
		//middleware.AuthMiddleware(),
		middleware.RateLimiterMiddleware(manager),
		//middleware.CircuitBreakerMiddleware(cbManager),
		middleware.HealthCheckMiddleware(),
		middleware.HealthControlMiddleware(),
	)

	userGroup := r.Group("/v1")
	{
		userGroup.GET("/keepalive", keepalive)
		userGroup.GET("/keepalive2", keepalive)
		userGroup.GET("/keepalive3", keepaliveError)
	}

	// 为特定路由设置限流规则
	err := manager.SetRouteLimiter("/v1", "5-M") // 特定路由限流：每分钟允许 5 次请求
	if err != nil {
		log.Fatalf("Failed to set route limiter: %v", err)
	}

}

func keepalive(c *gin.Context) {
	unify_id, _ := c.Get("unify_id")
	app.SetSuccess(c, unify_id)
}

func keepaliveError(c *gin.Context) {
	alarm.DingDingSystem("服务触发熔断, 请尽快排查问题", "服务触发熔断")
	app.SetErrorBreaker(c, "breaker test", 200)
}
