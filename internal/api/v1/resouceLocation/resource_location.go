package resouceLocation

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
	"wego-common-tmp/global"
	"wego-common-tmp/internal/model/requestModel"
	"wego-common-tmp/internal/model/responseModel"
	"wego-common-tmp/internal/service/v1/resourceLocation"
)

// RegisterResourceLocationRouter 注册资源位相关路由
func RegisterResourceLocationRouter(r *gin.Engine, manager *utils.RouteLimiterManager, cbManager *utils.CircuitBreakerManager) {
	resourceLocationRouter := r.Group("v1/resourceLocation")
	resourceLocationRouter.Use(
		middleware.Cors(),                         // 跨域中间件
		middleware.GinLogger(),                    // 日志中间件
		middleware.Recover(),                      // 异常恢复中间件
		middleware.RateLimiterMiddleware(manager), // 限流中间件
		middleware.AuthMiddleware(),
		middleware.CircuitBreakerMiddleware(cbManager), // 熔断中间件
	)
	resourceLocationRouter.POST("/GetResourceLocations", GetResourceLocations)
	// 配置熔断器
	cbManager.GetCircuitBreaker("/v1/resourceLocation/GetResourceLocations")
}

// GetResourceLocations 获取资源位列表
func GetResourceLocations(ctx *gin.Context) {
	var req requestModel.ResourceLocationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, global.BussinessErrorParam.ErrorCode)
		return
	}

	IsExamined := ctx.GetHeader("isexamined")
	ClientPlatform := ctx.GetHeader("clientplatform")
	AppChannel := ctx.GetHeader("appchannel")

	// 处理安卓逻辑
	if IsExamined == global.Examined && ClientPlatform == global.ClientPlatformAnDroid && (AppChannel == global.AppChannelHuawei || AppChannel == global.AppChannelVivo) {
		app.SetSuccess(ctx, nil)
		return
	}

	// 处理 ios 逻辑
	if IsExamined == global.Examined && ClientPlatform == global.ClientPlatformIOS {
		app.SetSuccess(ctx, nil)
		return
	}

	resourceLocations, err := resourceLocation.ResourceLocationService.GetResourceLocations(ctx, req)
	if err != nil {
		app.SetError(ctx, global.ResourceLocationInvisible.ErrorMsg, global.ResourceLocationInvisible.ErrorCode)
		return
	}

	var response responseModel.ResourceLocationListResponse
	response = append(response, resourceLocations...)
	app.SetSuccess(ctx, response)
}

// RefreshResourceLocationCache 刷新单个资源位缓存
func RefreshResourceLocationCache(ctx *gin.Context) {
	var req requestModel.RefreshResourceLocationCacheRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, global.BussinessErrorParam.ErrorCode)
		return
	}

	err := resourceLocation.ResourceLocationService.RefreshResourceLocationCache(ctx, req.ID)
	if err != nil {
		app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, global.BussinessErrorParam.ErrorCode)
		return
	}

	app.SetSuccess(ctx, nil)
}

// RefreshAllResourceLocationCache 刷新所有资源位缓存
func RefreshAllResourceLocationCache(ctx *gin.Context) {
	err := resourceLocation.ResourceLocationService.RefreshAllResourceLocationCache(ctx)
	if err != nil {
		app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, global.BussinessErrorParam.ErrorCode)
		return
	}

	app.SetSuccess(ctx, nil)
}
