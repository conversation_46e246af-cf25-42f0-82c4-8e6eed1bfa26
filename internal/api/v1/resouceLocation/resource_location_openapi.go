package resouceLocation

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"wego-common-tmp/global"
	"wego-common-tmp/internal/cache/appRouterInfoCache"
	"wego-common-tmp/internal/model/requestModel"
	"wego-common-tmp/internal/service/v1/resourceLocation"
)

// RegisterResourceLocationRouter 注册资源位相关路由
func RegisterResourceLocationOpenApiRouter(r *gin.Engine, manager *utils.RouteLimiterManager, cbManager *utils.CircuitBreakerManager) {
	resourceLocationRouter := r.Group("v1/resourceLocationOpenApi")
	resourceLocationRouter.Use(middleware.Cors(), // 跨域中间件
		middleware.GinLogger(),                    // 日志中间件
		middleware.Recover(),                      // 异常恢复中间件
		middleware.RateLimiterMiddleware(manager), // 限流中间件
		middleware.SignCheck(res, global.AppConfig.ServerConfig.Env),
		middleware.CircuitBreakerMiddleware(cbManager), // 熔断中间件
	)
	resourceLocationRouter.POST("/RefreshResourceLocationCacheByType", RefreshResourceLocationCacheByType)
	err := manager.SetRouteLimiter("/v1", "10-M") // 特定路由限流：每分钟允许 5 次请求
	if err != nil {
		log.AppLogger.Error("Failed to set route limiter: %v", zap.Error(err))
	}
}

// RefreshResourceLocationCacheByType 根据位置类型和资源类型刷新缓存
func RefreshResourceLocationCacheByType(ctx *gin.Context) {
	log.AppLogger.Error("RefreshResourceLocationCacheByType")
	var req requestModel.RefreshResourceLocationCacheRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, 600)
		return
	}

	err := resourceLocation.ResourceLocationService.RefreshResourceLocationCacheByType(ctx, req.PositionType, req.ResourceType)
	if err != nil {
		app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, global.BussinessErrorParam.ErrorCode)
		return
	}

	if req.AppRouterInfoId > 0 {
		err := appRouterInfoCache.AppRouterInfoService.RefreshAppRouterById(ctx, req.AppRouterInfoId)
		if err != nil {
			app.SetError(ctx, global.BussinessErrorParam.ErrorMsg, global.BussinessErrorParam.ErrorCode)
			return
		}
	}

	app.SetSuccess(ctx, nil)
}
