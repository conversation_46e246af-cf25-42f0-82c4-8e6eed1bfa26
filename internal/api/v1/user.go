package v1

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/middleware"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/gin-gonic/gin"
	"log"
	"strconv"
	v1 "wego-common-tmp/internal/service/v1"
)

func RegisterUserRouter(r *gin.Engine, manager *utils.RouteLimiterManager, cbManager *utils.CircuitBreakerManager) {
	// 注册中间件
	r.Use(
		middleware.Cors(),                         // 跨域中间件
		middleware.GinLogger(),                    // 日志中间件
		middleware.Recover(),                      // 异常恢复中间件
		middleware.RateLimiterMiddleware(manager), // 限流中间件
		//middleware.CircuitBreakerMiddleware(cbManager), // 熔断中间件
	)

	userGroup := r.Group("/v1")
	{
		userGroup.GET("/getUserInfo", getUserInfo)
	}

	// 为特定路由设置限流规则
	err := manager.SetRouteLimiter("/v1", "5-M") // 特定路由限流：每分钟允许 5 次请求
	if err != nil {
		log.Fatalf("Failed to set route limiter: %v", err)
	}

}

// 获取用户信息
func getUserInfo(c *gin.Context) {
	unifyId := c.Query("unifyId")

	if len(unifyId) == 0 {
		app.SetError(c, "unifyId不能为空", 400)
		return
	}

	unifyIdInt, err := strconv.Atoi(unifyId)
	if err != nil {
		app.SetError(c, "unifyId 必须是整数", 400)
		return
	}

	res := v1.UserService.UserInfo(c, unifyIdInt)
	app.SetSuccess(c, res)
}
