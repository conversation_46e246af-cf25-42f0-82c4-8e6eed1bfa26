package usercenterCache

import (
	"strconv"
)

type iUserCenterCacheKeyInterface interface {
	GetUcenterUIDKey(unifyId int) string
}

var UserCenterCacheKeyService iUserCenterCacheKeyInterface = &userCenterCacheKeyService{}

type userCenterCacheKeyService struct{}

// GetUcenterUIDKey 获取用户中心UID的缓存键
func (s *userCenterCacheKeyService) GetUcenterUIDKey(unifyId int) string {
	return "GetUcenterUID:" + strconv.Itoa(unifyId)
}
