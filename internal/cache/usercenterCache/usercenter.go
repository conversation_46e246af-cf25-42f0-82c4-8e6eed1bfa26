package usercenterCache

import (
	"context"
	"strconv"

	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"wego-common-tmp/internal/service/v1/usercenter"
)

type userCenterCacheInterface interface {
	GetUcenterUIDByUnifyID(ctx context.Context, unifyId int) (int64, error)
}

var UserCenterCacheService userCenterCacheInterface = &userCenterCacheService{}

type userCenterCacheService struct{}

// GetUcenterUIDByUnifyID 根据统一ID获取用户中心UID，使用Redis缓存结果2小时
func (s *userCenterCacheService) GetUcenterUIDByUnifyID(ctx context.Context, unifyId int) (int64, error) {
	// 获取缓存键
	cacheKey := UserCenterCacheKeyService.GetUcenterUIDKey(unifyId)

	// 使用Redis缓存，缓存时间为7200秒（2小时）
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		// 如果缓存不存在，则调用服务获取结果
		ucenterUID, err := usercenter.UserCenterService.GetUcenterUIDByUnifyID(ctx, unifyId)
		if err != nil {
			return "0"
		}

		// 返回结果
		return strconv.FormatInt(ucenterUID, 10)
	}, 7200) // 缓存7200秒（2小时）

	// 将字符串转换为int64
	ucenterUID, err := strconv.ParseInt(dataJson, 10, 64)
	if err != nil {
		return 0, err
	}

	return ucenterUID, nil
}
