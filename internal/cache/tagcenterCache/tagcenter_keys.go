package tagcenterCache

import (
	"strconv"
	"strings"
)

type iTagCenterCacheKeyInterface interface {
	GetIsUserInPackageKey(packageIDList []int, ucenterUID int64) string
}

var TagCenterCacheKeyService iTagCenterCacheKeyInterface = &tagCenterCacheKeyService{}

type tagCenterCacheKeyService struct{}

// GetIsUserInPackageKey 获取用户是否在包中的缓存键
func (s *tagCenterCacheKeyService) GetIsUserInPackageKey(packageIDList []int, ucenterUID int64) string {
	// 将包ID列表转换为字符串
	packageIDs := make([]string, len(packageIDList))
	for i, id := range packageIDList {
		packageIDs[i] = strconv.Itoa(id)
	}

	// 格式：IsUserInPackage:ucenterUID:packageID1_packageID2_...
	return "IsUserInPackage:" + strconv.FormatInt(ucenterUID, 10) + ":" + strings.Join(packageIDs, "_")
}
