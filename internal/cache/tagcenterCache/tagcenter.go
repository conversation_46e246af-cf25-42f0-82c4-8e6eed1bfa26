package tagcenterCache

import (
	"context"
	"strconv"
	"wego-common-tmp/internal/service/v1/tagcenter"

	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
)

type tagCenterCacheInterface interface {
	IsUserInPackage(ctx context.Context, packageIDList []int, ucenterUID int64) (bool, error)
}

var TagCenterCacheService tagCenterCacheInterface = &tagCenterCacheService{}

type tagCenterCacheService struct{}

// IsUserInPackage 检查用户是否在指定的包列表中，使用Redis缓存结果1分钟
func (s *tagCenterCacheService) IsUserInPackage(ctx context.Context, packageIDList []int, ucenterUID int64) (bool, error) {
	// 获取缓存键
	cacheKey := TagCenterCacheKeyService.GetIsUserInPackageKey(packageIDList, ucenterUID)

	// 使用Redis缓存，缓存时间为60秒
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		return tagcenter.TagCenterService.IsUserInPackage(ctx, packageIDList, ucenterUID)
	}, 60) // 缓存60秒

	// 将字符串转换为布尔值
	isInPackage, err := strconv.ParseBool(dataJson)
	if err != nil {
		return false, err
	}

	return isInPackage, nil
}
