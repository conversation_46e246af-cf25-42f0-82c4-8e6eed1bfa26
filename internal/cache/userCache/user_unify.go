package userCache

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/bytedance/sonic"
	"wego-common-tmp/internal/model/dbModel"
)

type userInterface interface {
	GetUserInfo(ctx context.Context, userId int) dbModel.WeappUserUnify
}

var UserService userInterface = &userService{}

type userService struct{}

func (s *userService) GetUserInfo(ctx context.Context, userId int) dbModel.WeappUserUnify {
	UserCacheKeyService.GetUserCacheKey(userId)
	dataJson := redisManager.RedisManagerService.Rmember(ctx, UserCacheKeyService.GetUserCacheKey(userId), func() string {
		return helper.AnyToString(dbModel.WeappUserUnifyModelService.GetUserInfo(userId))
	}, 60)
	var res dbModel.WeappUserUnify
	// 反序列化res为dbModel.WeappUserUnify
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		panic(err)
	}
	return res
}
