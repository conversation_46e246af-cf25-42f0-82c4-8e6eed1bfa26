package resourceLocationCache

import (
	"fmt"
)

type resourceLocationCacheKeyInterface interface {
	GetResourceLocationCacheKey(id int) string
	GetResourceLocationListCacheKey(positionType, resourceType int8) string
}

var ResourceLocationCacheKeyService resourceLocationCacheKeyInterface = &resourceLocationCacheKeyService{}

type resourceLocationCacheKeyService struct{}

// GetResourceLocationCacheKey 获取单个资源位的缓存键
func (s *resourceLocationCacheKeyService) GetResourceLocationCacheKey(id int) string {
	return "GetResourceLocationCacheKey" + fmt.Sprintf("%d", id)
}

// GetResourceLocationListCacheKey 获取资源位列表的缓存键
func (s *resourceLocationCacheKeyService) GetResourceLocationListCacheKey(positionType, resourceType int8) string {
	return "GetResourceLocationListCacheKey" + fmt.Sprintf("%d_%d", positionType, resourceType)
}
