package resourceLocationCache

import (
	"context"
	"wego-common-tmp/global"
	"wego-common-tmp/internal/cache/crowdPack"
	"wego-common-tmp/internal/cache/resourceLocationCrowdPackCache"
	"wego-common-tmp/internal/cache/tagcenterCache"
	"wego-common-tmp/internal/cache/usercenterCache"
	"wego-common-tmp/internal/model/dbModel"

	"gitee.com/jianmengkeji/wego-common-pkg/src/app"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/bytedance/sonic"
	"github.com/patrickmn/go-cache"
)

type resourceLocationInterface interface {
	GetResourceLocationByID(ctx context.Context, id int) (*dbModel.WeappResourceLocation, error)
	GetResourceLocationsByPositionAndType(ctx context.Context, positionType, resourceType int8) ([]*dbModel.WeappResourceLocation, error)
	IsResourceLocationVisible(ctx context.Context, resourceLocation *dbModel.WeappResourceLocation) (bool, error)
	RefreshResourceLocationCache(ctx context.Context, id int) error
	RefreshAllResourceLocationCache(ctx context.Context) error
	RefreshResourceLocationCacheByType(ctx context.Context, positionType, resourceType int8) error
	InitResouceLocationCache(ctx context.Context)
}

var ResourceLocationService resourceLocationInterface = &resourceLocationService{}

type resourceLocationService struct{}

// 本地缓存，永不过期
var localCache = cache.New(cache.NoExpiration, cache.NoExpiration)

func (s *resourceLocationService) InitResouceLocationCache(ctx context.Context) {
	_ = s.RefreshAllResourceLocationCache(ctx)
}

// GetResourceLocationByID 获取单个资源位信息
func (s *resourceLocationService) GetResourceLocationByID(ctx context.Context, id int) (*dbModel.WeappResourceLocation, error) {
	// 先从本地缓存获取
	if data, found := localCache.Get(ResourceLocationCacheKeyService.GetResourceLocationCacheKey(id)); found {
		return data.(*dbModel.WeappResourceLocation), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, ResourceLocationCacheKeyService.GetResourceLocationCacheKey(id), func() string {
		resourceLocation, err := dbModel.WeappResourceLocationModelService.GetResourceLocationByID(id)
		if err != nil {
			return ""
		}
		return helper.AnyToString(resourceLocation)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res dbModel.WeappResourceLocation
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(ResourceLocationCacheKeyService.GetResourceLocationCacheKey(id), &res, cache.NoExpiration)

	return &res, nil
}

// GetResourceLocationsByPositionAndType 根据位置类型和资源类型获取资源位列表
func (s *resourceLocationService) GetResourceLocationsByPositionAndType(ctx context.Context, positionType, resourceType int8) ([]*dbModel.WeappResourceLocation, error) {
	cacheKey := ResourceLocationCacheKeyService.GetResourceLocationListCacheKey(positionType, resourceType)
	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.([]*dbModel.WeappResourceLocation), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		resourceLocations, err := dbModel.WeappResourceLocationModelService.GetResourceLocationsByPositionAndType(positionType, resourceType)
		if err != nil {
			return ""
		}
		return helper.AnyToString(resourceLocations)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res []*dbModel.WeappResourceLocation
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, res, cache.NoExpiration)

	return res, nil
}

// RefreshResourceLocationCache 刷新单个资源位缓存
func (s *resourceLocationService) RefreshResourceLocationCache(ctx context.Context, id int) error {
	// 从数据库获取最新数据
	resourceLocation, err := dbModel.WeappResourceLocationModelService.GetResourceLocationByID(id)
	if err != nil {
		return err
	}

	// 更新Redis缓存
	jsonData := helper.AnyToString(resourceLocation)
	redisManager.RedisManagerService.Set(ctx, ResourceLocationCacheKeyService.GetResourceLocationCacheKey(id), jsonData, 3600)

	// 更新本地缓存
	localCache.Set(ResourceLocationCacheKeyService.GetResourceLocationCacheKey(id), resourceLocation, cache.NoExpiration)

	// 刷新所有列表缓存
	return s.RefreshAllResourceLocationCache(ctx)
}

// RefreshAllResourceLocationCache 刷新所有资源位缓存
func (s *resourceLocationService) RefreshAllResourceLocationCache(ctx context.Context) error {
	// 从数据库获取所有资源位
	resourceLocations, err := dbModel.WeappResourceLocationModelService.GetAllResourceLocations()
	if err != nil {
		return err
	}

	// 按位置类型和资源类型分组
	resourceMap := make(map[string][]*dbModel.WeappResourceLocation)
	for _, resource := range resourceLocations {
		if resource.PositionType != nil && resource.ResourceType != nil {
			key := ResourceLocationCacheKeyService.GetResourceLocationListCacheKey(*resource.PositionType, *resource.ResourceType)
			if _, ok := resourceMap[key]; !ok {
				resourceMap[key] = make([]*dbModel.WeappResourceLocation, 0)
			}
			if *resource.Status == 1 { // 只缓存状态为1的资源位
				resourceMap[key] = append(resourceMap[key], resource)
			}
		}

		// 更新单个资源位缓存
		jsonData := helper.AnyToString(resource)
		redisManager.RedisManagerService.Set(ctx, ResourceLocationCacheKeyService.GetResourceLocationCacheKey(resource.Id), jsonData, 3600)
		localCache.Set(ResourceLocationCacheKeyService.GetResourceLocationCacheKey(resource.Id), resource, cache.NoExpiration)
	}

	// 更新列表缓存
	for key, resources := range resourceMap {
		jsonData := helper.AnyToString(resources)
		redisManager.RedisManagerService.Set(ctx, key, jsonData, 3600)
		localCache.Set(key, resources, cache.NoExpiration)
	}

	return nil
}

// IsResourceLocationVisible 判断资源位是否对特定用户可见
func (s *resourceLocationService) IsResourceLocationVisible(ctx context.Context, resourceLocation *dbModel.WeappResourceLocation) (bool, error) {
	// 检查资源位状态
	if resourceLocation.Status == nil || *resourceLocation.Status != 1 {
		return false, nil
	}

	// 根据人群包版本判断
	if resourceLocation.CrowdPackVersion == 1 {

		// 如果没有人群包ID，则对所有人可见
		if resourceLocation.CrowdPackId == nil || *resourceLocation.CrowdPackId == 0 {
			return true, nil
		}

		// 老版本：检查用户手机号是否在人群包中
		phone := ctx.Value("userTokenInfo").(app.UserTokenInfo).Phone
		isInCrowdPack, err := crowdPack.CrowdPackService.CheckPhoneInCrowdPack(ctx, *resourceLocation.CrowdPackId, phone)
		if err != nil {
			return false, err
		}
		return isInCrowdPack, nil
	} else if resourceLocation.CrowdPackVersion == 2 {
		// 获取对应的人群包ID
		crowdPackInfo, err := resourceLocationCrowdPackCache.ResourceLocationCrowdPackService.GetCrowdPacksByResourceLocationID(ctx, resourceLocation.Id)

		if err != nil {
			return false, err
		}

		if crowdPackInfo.Id == 0 {
			return true, nil
		}

		PackageIDList := crowdPackInfo.CrowdPackId

		if PackageIDList == "" {
			return true, nil
		}

		// 将数组中的string转为int
		var PackageIDListInt []int

		PackageIDListInt, err = global.JsonStrToIntSlice(PackageIDList)

		if err != nil {
			return false, err
		}

		// 新版本：调用外部接口
		// 这里应该调用外部接口，但由于没有具体的接口信息，暂时返回true
		// TODO: 实现外部接口调用
		unifyId := ctx.Value("userTokenInfo").(app.UserTokenInfo).UnifyID
		if unifyId == 0 {
			return false, nil
		}

		// 使用缓存服务获取ucenter_uid
		ucenter_uid, err := usercenterCache.UserCenterCacheService.GetUcenterUIDByUnifyID(ctx, unifyId)
		if err != nil {
			return false, err
		}

		// 使用新的TagCenter服务检查用户是否在包中
		isInPackage, err := tagcenterCache.TagCenterCacheService.IsUserInPackage(ctx, PackageIDListInt, ucenter_uid)
		if err != nil {
			return false, err
		}

		if isInPackage {

			if crowdPackInfo.DeliveryMode == dbModel.CROWDPACK_DELIVERY_MODEL_CONTAIN {
				return true, nil
			} else {
				return false, nil
			}

		} else {

			if crowdPackInfo.DeliveryMode == dbModel.CROWDPACK_DELIVERY_MODEL_EXCLUDE {
				return true, nil
			} else {
				return false, nil
			}
		}
	}

	return false, nil
}

// RefreshResourceLocationCacheByType 根据位置类型和资源类型刷新缓存
func (s *resourceLocationService) RefreshResourceLocationCacheByType(ctx context.Context, positionType, resourceType int8) error {
	// 从数据库获取指定类型的资源位
	resourceLocations, err := dbModel.WeappResourceLocationModelService.GetResourceLocationsByPositionAndType(positionType, resourceType)
	if err != nil {
		return err
	}

	// 更新Redis缓存
	cacheKey := ResourceLocationCacheKeyService.GetResourceLocationListCacheKey(positionType, resourceType)
	jsonData := helper.AnyToString(resourceLocations)
	redisManager.RedisManagerService.Set(ctx, cacheKey, jsonData, 3600)

	// 更新本地缓存
	localCache.Set(cacheKey, resourceLocations, cache.NoExpiration)

	// 同时更新单个资源位的缓存
	for _, resource := range resourceLocations {
		// 更新单个资源位缓存
		singleJsonData := helper.AnyToString(resource)
		redisManager.RedisManagerService.Set(ctx, ResourceLocationCacheKeyService.GetResourceLocationCacheKey(resource.Id), singleJsonData, 3600)
		localCache.Set(ResourceLocationCacheKeyService.GetResourceLocationCacheKey(resource.Id), resource, cache.NoExpiration)
	}

	return nil
}
