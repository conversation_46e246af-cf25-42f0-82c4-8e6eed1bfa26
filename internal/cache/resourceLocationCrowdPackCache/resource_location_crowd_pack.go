package resourceLocationCrowdPackCache

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/bytedance/sonic"
	"github.com/patrickmn/go-cache"
	"time"
	"wego-common-tmp/internal/model/dbModel"
)

type resourceLocationCrowdPackInterface interface {
	GetCrowdPacksByResourceLocationID(ctx context.Context, resourceLocationID int) (*dbModel.WeappResourceLocationCrowdPack, error)
	RefreshResourceLocationCrowdPackCache(ctx context.Context, resourceLocationID int) error
}

var ResourceLocationCrowdPackService resourceLocationCrowdPackInterface = &resourceLocationCrowdPackService{}

type resourceLocationCrowdPackService struct{}

// 本地缓存，永不过期
var localCache = cache.New(cache.NoExpiration, cache.NoExpiration)

// 初始化函数
func init() {
	// 每小时刷新一次本地缓存
	go func() {
		ticker := time.NewTicker(1 * time.Hour)
		for range ticker.C {
			// 这里不刷新所有缓存，因为资源位ID可能很多，只在需要时刷新
		}
	}()
}

// GetCrowdPacksByResourceLocationID 获取资源位关联的人群包列表（带缓存）
func (s *resourceLocationCrowdPackService) GetCrowdPacksByResourceLocationID(ctx context.Context, resourceLocationID int) (*dbModel.WeappResourceLocationCrowdPack, error) {
	cacheKey := ResourceLocationCrowdPackCacheKeyService.GetResourceLocationCrowdPackListCacheKey(resourceLocationID)

	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.(*dbModel.WeappResourceLocationCrowdPack), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		crowdPacks, err := dbModel.WeappResourceLocationCrowdPackModelService.GetCrowdPacksByResourceLocationID(resourceLocationID)
		if err != nil {
			return ""
		}
		return helper.AnyToString(crowdPacks)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res *dbModel.WeappResourceLocationCrowdPack
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, res, cache.NoExpiration)

	return res, nil
}

// RefreshResourceLocationCrowdPackCache 刷新资源位关联的人群包缓存
func (s *resourceLocationCrowdPackService) RefreshResourceLocationCrowdPackCache(ctx context.Context, resourceLocationID int) error {
	// 从数据库获取最新数据
	crowdPacks, err := dbModel.WeappResourceLocationCrowdPackModelService.GetCrowdPacksByResourceLocationID(resourceLocationID)
	if err != nil {
		return err
	}

	// 更新Redis缓存
	cacheKey := ResourceLocationCrowdPackCacheKeyService.GetResourceLocationCrowdPackListCacheKey(resourceLocationID)
	jsonData := helper.AnyToString(crowdPacks)
	redisManager.RedisManagerService.Set(ctx, cacheKey, jsonData, 3600)

	// 更新本地缓存
	localCache.Set(cacheKey, crowdPacks, cache.NoExpiration)

	return nil
}
