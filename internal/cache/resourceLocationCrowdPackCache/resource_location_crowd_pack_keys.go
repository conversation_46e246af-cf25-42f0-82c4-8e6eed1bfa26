package resourceLocationCrowdPackCache

import (
	"strconv"
)

type resourceLocationCrowdPackCacheKeyInterface interface {
	GetResourceLocationCrowdPackListCacheKey(resourceLocationID int) string
}

var ResourceLocationCrowdPackCacheKeyService resourceLocationCrowdPackCacheKeyInterface = &resourceLocationCrowdPackCacheKeyService{}

type resourceLocationCrowdPackCacheKeyService struct{}

// GetResourceLocationCrowdPackListCacheKey 获取资源位关联人群包列表的缓存键
func (s *resourceLocationCrowdPackCacheKeyService) GetResourceLocationCrowdPackListCacheKey(resourceLocationID int) string {
	return "ResourceLocationCrowdPack:List:ResourceLocationID:" + strconv.Itoa(resourceLocationID)
}
