package appRouterInfoCache

import "fmt"

type IAppRouterInfoCacheKeyService interface {
	// GetAppRouterInfoCacheKey 获取单个app路由信息缓存key
	GetAppRouterInfoCacheKey(id int) string
	// GetLatestAppRouterInfoByTemplateIDCacheKey 获取根据模板ID获取最新路由信息的缓存key
	GetLatestAppRouterInfoByTemplateIDCacheKey(templateId int) string
	// GetAppRouterInfosByTemplateIDCacheKey 获取根据模板ID获取路由列表的缓存key
	GetAppRouterInfosByTemplateIDCacheKey(templateId int) string
	// GetAppRouterInfoByRouterCacheKey 获取根据路由字符串获取路由信息的缓存key
	GetAppRouterInfoByRouterCacheKey(router string) string
}

var AppRouterInfoCacheKeyService IAppRouterInfoCacheKeyService = &appRouterInfoCacheKeyService{}

type appRouterInfoCacheKeyService struct{}

// GetAppRouterInfoCacheKey 获取单个app路由信息缓存key
func (s *appRouterInfoCacheKeyService) GetAppRouterInfoCacheKey(id int) string {
	return fmt.Sprintf("app_router_info:id:%d", id)
}

// GetLatestAppRouterInfoByTemplateIDCacheKey 获取根据模板ID获取最新路由信息的缓存key
func (s *appRouterInfoCacheKeyService) GetLatestAppRouterInfoByTemplateIDCacheKey(templateId int) string {
	return fmt.Sprintf("app_router_info:latest:template_id:%d", templateId)
}

// GetAppRouterInfosByTemplateIDCacheKey 获取根据模板ID获取路由列表的缓存key
func (s *appRouterInfoCacheKeyService) GetAppRouterInfosByTemplateIDCacheKey(templateId int) string {
	return fmt.Sprintf("app_router_info:list:template_id:%d", templateId)
}

// GetAppRouterInfoByRouterCacheKey 获取根据路由字符串获取路由信息的缓存key
func (s *appRouterInfoCacheKeyService) GetAppRouterInfoByRouterCacheKey(router string) string {
	return fmt.Sprintf("app_router_info:router:%s", router)
}
