package appRouterInfoCache

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/bytedance/sonic"
	"github.com/patrickmn/go-cache"
	"time"
	"wego-common-tmp/internal/model/dbModel"
)

// 本地缓存实例
var localCache = cache.New(cache.NoExpiration, 10*time.Minute)

type IAppRouterInfoService interface {
	// GetAppRouterInfoByID 获取单个app路由信息（带缓存）
	GetAppRouterInfoByID(ctx context.Context, id int) (*dbModel.WeappAppRouterInfo, error)
	// GetLatestAppRouterInfoByTemplateID 根据模板ID获取最新app路由信息（带缓存）
	GetLatestAppRouterInfoByTemplateID(ctx context.Context, templateId int) (*dbModel.WeappAppRouterInfo, error)
	// GetAppRouterInfosByTemplateID 根据模板ID获取app路由列表（带缓存）
	GetAppRouterInfosByTemplateID(ctx context.Context, templateId int) ([]*dbModel.WeappAppRouterInfo, error)
	// GetAppRouterInfoByRouter 根据路由字符串获取路由信息（带缓存）
	GetAppRouterInfoByRouter(ctx context.Context, router string) (*dbModel.WeappAppRouterInfo, error)
	// RefreshAppRouterInfoCache 刷新单个app路由信息缓存
	RefreshAppRouterInfoCache(ctx context.Context, id int)
	// RefreshLatestAppRouterInfoByTemplateIDCache 刷新根据模板ID获取最新路由信息的缓存
	RefreshLatestAppRouterInfoByTemplateIDCache(ctx context.Context, templateId int)
	// RefreshAppRouterInfosByTemplateIDCache 刷新根据模板ID获取路由列表的缓存
	RefreshAppRouterInfosByTemplateIDCache(ctx context.Context, templateId int)
	// InitAllAppRouterInfoCache 初始化所有路由信息缓存，重建本地缓存和Redis缓存
	InitAllAppRouterInfoCache(ctx context.Context) error
	// RefreshAppRouterById 刷新单个app路由信息缓存
	RefreshAppRouterById(ctx context.Context, id int) error
}

var AppRouterInfoService IAppRouterInfoService = &appRouterInfoService{}

type appRouterInfoService struct{}

// GetAppRouterInfoByID 获取单个app路由信息（带缓存）
func (s *appRouterInfoService) GetAppRouterInfoByID(ctx context.Context, id int) (*dbModel.WeappAppRouterInfo, error) {
	cacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfoCacheKey(id)

	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.(*dbModel.WeappAppRouterInfo), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		routerInfo, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByID(id)
		if err != nil {
			return ""
		}
		return helper.AnyToString(routerInfo)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res dbModel.WeappAppRouterInfo
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, &res, cache.NoExpiration)

	return &res, nil
}

// GetLatestAppRouterInfoByTemplateID 根据模板ID获取最新app路由信息（带缓存）
func (s *appRouterInfoService) GetLatestAppRouterInfoByTemplateID(ctx context.Context, templateId int) (*dbModel.WeappAppRouterInfo, error) {
	cacheKey := AppRouterInfoCacheKeyService.GetLatestAppRouterInfoByTemplateIDCacheKey(templateId)

	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.(*dbModel.WeappAppRouterInfo), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		routerInfo, err := dbModel.WeappAppRouterInfoModelService.GetLatestAppRouterInfoByTemplateID(templateId)
		if err != nil {
			return ""
		}
		return helper.AnyToString(routerInfo)
	}, 1800) // 30分钟过期（最新数据变化较频繁，缓存时间稍短）

	if dataJson == "" {
		return nil, nil
	}

	var res dbModel.WeappAppRouterInfo
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, &res, cache.NoExpiration)

	return &res, nil
}

// GetAppRouterInfosByTemplateID 根据模板ID获取app路由列表（带缓存）
func (s *appRouterInfoService) GetAppRouterInfosByTemplateID(ctx context.Context, templateId int) ([]*dbModel.WeappAppRouterInfo, error) {
	cacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfosByTemplateIDCacheKey(templateId)

	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.([]*dbModel.WeappAppRouterInfo), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		routerInfos, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfosByTemplateID(templateId)
		if err != nil {
			return ""
		}
		return helper.AnyToString(routerInfos)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res []*dbModel.WeappAppRouterInfo
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, res, cache.NoExpiration)

	return res, nil
}

// GetAppRouterInfoByRouter 根据路由字符串获取路由信息（带缓存）
func (s *appRouterInfoService) GetAppRouterInfoByRouter(ctx context.Context, router string) (*dbModel.WeappAppRouterInfo, error) {
	cacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfoByRouterCacheKey(router)

	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.(*dbModel.WeappAppRouterInfo), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		routerInfo, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByRouter(router)
		if err != nil {
			return ""
		}
		return helper.AnyToString(routerInfo)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res dbModel.WeappAppRouterInfo
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, &res, cache.NoExpiration)

	return &res, nil
}

// RefreshAppRouterInfoCache 刷新单个app路由信息缓存
func (s *appRouterInfoService) RefreshAppRouterInfoCache(ctx context.Context, id int) {
	cacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfoCacheKey(id)

	// 删除本地缓存
	localCache.Delete(cacheKey)

	// 删除Redis缓存
	redisManager.RedisManagerService.Del(ctx, cacheKey)
}

// RefreshLatestAppRouterInfoByTemplateIDCache 刷新根据模板ID获取最新路由信息的缓存
func (s *appRouterInfoService) RefreshLatestAppRouterInfoByTemplateIDCache(ctx context.Context, templateId int) {
	cacheKey := AppRouterInfoCacheKeyService.GetLatestAppRouterInfoByTemplateIDCacheKey(templateId)

	// 删除本地缓存
	localCache.Delete(cacheKey)

	// 删除Redis缓存
	redisManager.RedisManagerService.Del(ctx, cacheKey)
}

// RefreshAppRouterInfosByTemplateIDCache 刷新根据模板ID获取路由列表的缓存
func (s *appRouterInfoService) RefreshAppRouterInfosByTemplateIDCache(ctx context.Context, templateId int) {
	cacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfosByTemplateIDCacheKey(templateId)

	// 删除本地缓存
	localCache.Delete(cacheKey)

	// 删除Redis缓存
	redisManager.RedisManagerService.Del(ctx, cacheKey)
}

// InitAllAppRouterInfoCache 初始化所有路由信息缓存，采用本地缓存覆盖的方式重建缓存
func (s *appRouterInfoService) InitAllAppRouterInfoCache(ctx context.Context) error {
	// 1. 获取所有正常状态的路由信息（不清空本地缓存，采用覆盖方式）
	allRouterInfos, err := dbModel.WeappAppRouterInfoModelService.GetAllAppRouterInfos()
	if err != nil {
		return err
	}

	for _, routerInfo := range allRouterInfos {
		// 覆盖路由列表缓存
		listCacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfoCacheKey(routerInfo.Id)
		listDataJson := helper.AnyToString(routerInfo)

		// 覆盖Redis缓存
		redisManager.RedisManagerService.Set(ctx, listCacheKey, listDataJson, 3600) // 1小时过期

		// 覆盖本地缓存
		localCache.Set(listCacheKey, routerInfo, cache.NoExpiration)
	}
	return nil
}

// InitAllAppRouterInfoCache 初始化所有路由信息缓存，采用本地缓存覆盖的方式重建缓存
func (s *appRouterInfoService) RefreshAppRouterById(ctx context.Context, id int) error {
	// 1. 获取所有正常状态的路由信息（不清空本地缓存，采用覆盖方式）
	routerInfo, err := dbModel.WeappAppRouterInfoModelService.GetAppRouterInfoByID(id)
	if err != nil {
		return err
	}

	// 覆盖路由列表缓存
	listCacheKey := AppRouterInfoCacheKeyService.GetAppRouterInfoCacheKey(routerInfo.Id)
	listDataJson := helper.AnyToString(routerInfo)

	// 覆盖Redis缓存
	redisManager.RedisManagerService.Set(ctx, listCacheKey, listDataJson, 3600) // 1小时过期

	// 覆盖本地缓存
	localCache.Set(listCacheKey, routerInfo, cache.NoExpiration)

	return nil

}
