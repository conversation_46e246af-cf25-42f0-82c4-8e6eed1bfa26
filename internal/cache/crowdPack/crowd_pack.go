package crowdPack

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/cache/redisManager"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"github.com/bytedance/sonic"
	"github.com/patrickmn/go-cache"
	"wego-common-tmp/internal/model/dbModel"
)

type crowdPackInterface interface {
	GetCrowdPackByID(ctx context.Context, id int) (*dbModel.WeappCrowdPack, error)
	CheckPhoneInCrowdPack(ctx context.Context, crowdPackId int, phone string) (bool, error)
	RefreshCrowdPackCache(ctx context.Context, id int) error
	RefreshAllCrowdPackCache(ctx context.Context) error
	InitCrowdPackCache(ctx context.Context)
}

var CrowdPackService crowdPackInterface = &crowdPackService{}

type crowdPackService struct{}

// 本地缓存，永不过期
var localCache = cache.New(cache.NoExpiration, cache.NoExpiration)

func (s *crowdPackService) InitCrowdPackCache(ctx context.Context) {
	_ = s.RefreshAllCrowdPackCache(ctx)
}

// GetCrowdPackByID 获取单个人群包信息
func (s *crowdPackService) GetCrowdPackByID(ctx context.Context, id int) (*dbModel.WeappCrowdPack, error) {
	// 先从本地缓存获取
	if data, found := localCache.Get(CrowdPackCacheKeyService.GetCrowdPackCacheKey(id)); found {
		return data.(*dbModel.WeappCrowdPack), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, CrowdPackCacheKeyService.GetCrowdPackCacheKey(id), func() string {
		crowdPack, err := dbModel.WeappCrowdPackModelService.GetCrowdPackByID(id)
		if err != nil {
			return ""
		}
		return helper.AnyToString(crowdPack)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return nil, nil
	}

	var res dbModel.WeappCrowdPack
	err := sonic.Unmarshal([]byte(dataJson), &res)
	if err != nil {
		return nil, err
	}

	// 存入本地缓存
	localCache.Set(CrowdPackCacheKeyService.GetCrowdPackCacheKey(id), &res, cache.NoExpiration)

	return &res, nil
}

// CheckPhoneInCrowdPack 检查手机号是否在人群包中
func (s *crowdPackService) CheckPhoneInCrowdPack(ctx context.Context, crowdPackId int, phone string) (bool, error) {
	// 从缓存获取结果
	cacheKey := CrowdPackCacheKeyService.GetPhoneInCrowdPackCacheKey(crowdPackId, phone)

	// 先从本地缓存获取
	if data, found := localCache.Get(cacheKey); found {
		return data.(bool), nil
	}

	// 从Redis获取
	dataJson := redisManager.RedisManagerService.Rmember(ctx, cacheKey, func() string {
		isInCrowdPack, err := dbModel.WeappCrowdPackModelService.CheckPhoneInCrowdPack(crowdPackId, phone)
		if err != nil {
			return "false"
		}
		return helper.AnyToString(isInCrowdPack)
	}, 3600) // 1小时过期

	if dataJson == "" {
		return false, nil
	}

	var result bool
	err := sonic.Unmarshal([]byte(dataJson), &result)
	if err != nil {
		return false, err
	}

	// 存入本地缓存
	localCache.Set(cacheKey, result, cache.NoExpiration)

	return result, nil
}

// RefreshCrowdPackCache 刷新单个人群包缓存
func (s *crowdPackService) RefreshCrowdPackCache(ctx context.Context, id int) error {
	// 从数据库获取最新数据
	crowdPack, err := dbModel.WeappCrowdPackModelService.GetCrowdPackByID(id)
	if err != nil {
		return err
	}

	// 更新Redis缓存
	jsonData := helper.AnyToString(crowdPack)
	redisManager.RedisManagerService.Set(ctx, CrowdPackCacheKeyService.GetCrowdPackCacheKey(id), jsonData, 3600)

	// 更新本地缓存
	localCache.Set(CrowdPackCacheKeyService.GetCrowdPackCacheKey(id), crowdPack, cache.NoExpiration)

	return nil
}

// RefreshAllCrowdPackCache 刷新所有人群包缓存
func (s *crowdPackService) RefreshAllCrowdPackCache(ctx context.Context) error {
	// 从数据库获取所有人群包
	crowdPacks, err := dbModel.WeappCrowdPackModelService.GetAllCrowdPacks()
	if err != nil {
		return err
	}

	// 更新缓存
	for _, crowdPack := range crowdPacks {
		// 更新单个人群包缓存
		jsonData := helper.AnyToString(crowdPack)
		redisManager.RedisManagerService.Set(ctx, CrowdPackCacheKeyService.GetCrowdPackCacheKey(crowdPack.Id), jsonData, 3600)
		localCache.Set(CrowdPackCacheKeyService.GetCrowdPackCacheKey(crowdPack.Id), crowdPack, cache.NoExpiration)
	}

	return nil
}
