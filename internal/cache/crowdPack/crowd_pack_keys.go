package crowdPack

import (
	"strconv"
)

type iCrowdPackCacheKeyInterface interface {
	GetCrowdPackCacheKey(id int) string
	GetPhoneInCrowdPackCacheKey(crowdPackId int, phone string) string
}

var CrowdPackCacheKeyService iCrowdPackCacheKeyInterface = &crowdPackCacheKeyService{}

type crowdPackCacheKeyService struct{}

func (s *crowdPackCacheKeyService) GetCrowdPackCacheKey(id int) string {
	return "CrowdPack:ID:" + strconv.Itoa(id)
}

func (s *crowdPackCacheKeyService) GetPhoneInCrowdPackCacheKey(crowdPackId int, phone string) string {
	return "CrowdPack:Phone:" + strconv.Itoa(crowdPackId) + ":" + phone
}

// 通过人群包ID获取key
