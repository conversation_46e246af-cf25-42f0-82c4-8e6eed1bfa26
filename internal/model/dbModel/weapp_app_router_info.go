package dbModel

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"time"
)

const (
	// APP路由状态常量
	APP_ROUTER_STATE_DELETED = 0 // 删除
	APP_ROUTER_STATE_NORMAL  = 1 // 正常
)

// WeappAppRouterInfo app路由模板生成的路由详情表
type WeappAppRouterInfo struct {
	// Id 主键ID
	Id int `gorm:"column:id;type:INT(10) UNSIGNED;not null;primaryKey;autoIncrement;comment:'-'" json:"id"`
	// AppRouterTemplateId app路由模板ID
	AppRouterTemplateId int `gorm:"column:app_router_template_id;type:INT(11);not null;default:0;comment:'app路由模板ID'" json:"app_router_template_id"`
	// Router app路由
	Router string `gorm:"column:router;type:VARCHAR(255);not null;default:'';comment:'app路由'" json:"router"`
	// State 状态：1-正常，0-删除
	State int8 `gorm:"column:state;type:TINYINT(4);not null;default:1;comment:'状态：1-正常，0-删除'" json:"state"`
	// CreatedAt 创建时间
	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME;not null;default:CURRENT_TIMESTAMP;comment:'-'" json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME;not null;default:CURRENT_TIMESTAMP;comment:'-'" json:"updated_at"`
	// Creator 创建人
	Creator int `gorm:"column:creator;type:INT(11);not null;default:0;comment:'-'" json:"creator"`
	// Updater 更新人
	Updater int `gorm:"column:updater;type:INT(11);not null;default:0;comment:'-'" json:"updater"`
}

// TableName 设置WeappAppRouterInfo的表名为`weapp_app_router_info`
func (WeappAppRouterInfo) TableName() string {
	return "weapp_app_router_info"
}

type weappAppRouterInfoModelInterface interface {
	// GetAppRouterInfoByID 根据ID获取app路由信息
	GetAppRouterInfoByID(id int) (*WeappAppRouterInfo, error)
	// GetAppRouterInfosByTemplateID 根据模板ID获取app路由列表
	GetAppRouterInfosByTemplateID(templateId int) ([]*WeappAppRouterInfo, error)
	// GetLatestAppRouterInfoByTemplateID 根据模板ID获取最新一条app路由信息
	GetLatestAppRouterInfoByTemplateID(templateId int) (*WeappAppRouterInfo, error)
	// GetAllAppRouterInfos 获取所有正常状态的app路由信息
	GetAllAppRouterInfos() ([]*WeappAppRouterInfo, error)
	// CreateAppRouterInfo 创建app路由信息
	CreateAppRouterInfo(routerInfo *WeappAppRouterInfo) error
	// UpdateAppRouterInfo 更新app路由信息
	UpdateAppRouterInfo(routerInfo *WeappAppRouterInfo) error
	// DeleteAppRouterInfo 软删除app路由信息
	DeleteAppRouterInfo(id int, updater int) error
	// GetAppRouterInfoByRouter 根据路由字符串获取路由信息
	GetAppRouterInfoByRouter(router string) (*WeappAppRouterInfo, error)
}

var WeappAppRouterInfoModelService weappAppRouterInfoModelInterface = &weappAppRouterInfoModelService{}

type weappAppRouterInfoModelService struct{}

// GetAppRouterInfoByID 根据ID获取app路由信息
func (s *weappAppRouterInfoModelService) GetAppRouterInfoByID(id int) (*WeappAppRouterInfo, error) {
	var routerInfo WeappAppRouterInfo
	if err := mysql.MysqlDb.Where("id = ? AND state = ?", id, APP_ROUTER_STATE_NORMAL).First(&routerInfo).Error; err != nil {
		return nil, err
	}
	return &routerInfo, nil
}

// GetAppRouterInfosByTemplateID 根据模板ID获取app路由列表
func (s *weappAppRouterInfoModelService) GetAppRouterInfosByTemplateID(templateId int) ([]*WeappAppRouterInfo, error) {
	var routerInfos []*WeappAppRouterInfo
	if err := mysql.MysqlDb.Where("id = ? AND state = ?", templateId, APP_ROUTER_STATE_NORMAL).
		Order("created_at DESC").Find(&routerInfos).Error; err != nil {
		return nil, err
	}
	return routerInfos, nil
}

// GetLatestAppRouterInfoByTemplateID 根据模板ID获取最新一条app路由信息
func (s *weappAppRouterInfoModelService) GetLatestAppRouterInfoByTemplateID(templateId int) (*WeappAppRouterInfo, error) {
	var routerInfo WeappAppRouterInfo
	if err := mysql.MysqlDb.Where("id = ? AND state = ?", templateId, APP_ROUTER_STATE_NORMAL).
		Order("created_at DESC").First(&routerInfo).Error; err != nil {
		return nil, err
	}
	return &routerInfo, nil
}

// GetAllAppRouterInfos 获取所有正常状态的app路由信息
func (s *weappAppRouterInfoModelService) GetAllAppRouterInfos() ([]*WeappAppRouterInfo, error) {
	var routerInfos []*WeappAppRouterInfo
	if err := mysql.MysqlDb.Where("state = ?", APP_ROUTER_STATE_NORMAL).
		Order("created_at DESC").Find(&routerInfos).Error; err != nil {
		return nil, err
	}
	return routerInfos, nil
}

// CreateAppRouterInfo 创建app路由信息
func (s *weappAppRouterInfoModelService) CreateAppRouterInfo(routerInfo *WeappAppRouterInfo) error {
	routerInfo.State = APP_ROUTER_STATE_NORMAL
	routerInfo.CreatedAt = time.Now()
	routerInfo.UpdatedAt = time.Now()

	if err := mysql.MysqlDb.Create(routerInfo).Error; err != nil {
		return err
	}
	return nil
}

// UpdateAppRouterInfo 更新app路由信息
func (s *weappAppRouterInfoModelService) UpdateAppRouterInfo(routerInfo *WeappAppRouterInfo) error {
	routerInfo.UpdatedAt = time.Now()

	if err := mysql.MysqlDb.Save(routerInfo).Error; err != nil {
		return err
	}
	return nil
}

// DeleteAppRouterInfo 软删除app路由信息
func (s *weappAppRouterInfoModelService) DeleteAppRouterInfo(id int, updater int) error {
	updates := map[string]interface{}{
		"state":      APP_ROUTER_STATE_DELETED,
		"updater":    updater,
		"updated_at": time.Now(),
	}

	if err := mysql.MysqlDb.Model(&WeappAppRouterInfo{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return err
	}
	return nil
}

// GetAppRouterInfoByRouter 根据路由字符串获取路由信息
func (s *weappAppRouterInfoModelService) GetAppRouterInfoByRouter(router string) (*WeappAppRouterInfo, error) {
	var routerInfo WeappAppRouterInfo
	if err := mysql.MysqlDb.Where("router = ? AND state = ?", router, APP_ROUTER_STATE_NORMAL).First(&routerInfo).Error; err != nil {
		return nil, err
	}
	return &routerInfo, nil
}
