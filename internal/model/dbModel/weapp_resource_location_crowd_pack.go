package dbModel

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"time"
)

const (
	CROWDPACK_DELIVERY_MODEL_CONTAIN = 1 //投放模式 包含

	CROWDPACK_DELIVERY_MODEL_EXCLUDE = 2 //投放模式 排除)
)

// WeappResourceLocationCrowdPack 资源位人群包映射表
type WeappResourceLocationCrowdPack struct {
	Id int `gorm:"column:id;type:INT(11);not null;primaryKey" json:"id"`
	// WithoutId 资源位id
	WithoutId int `gorm:"column:without_id;type:INT(11);not null;default:0;comment:'资源位id'" json:"without_id"`
	// CrowdPackId 人群包id
	CrowdPackId string `gorm:"column:crowd_pack_id;type:TEXT;not null;comment:'人群包id'" json:"crowd_pack_id"`
	// DeliveryMode 投放模式 1 包含 2排除
	DeliveryMode int8 `gorm:"column:delivery_mode;type:TINYINT(4);not null;default:1;comment:'投放模式 1 包含 2排除'" json:"delivery_mode"`
	// State 状态 0 无效 1有效
	State int8 `gorm:"column:state;type:TINYINT(4);not null;default:1;comment:'状态 0 无效 1有效'" json:"state"`
	// CreatedAt 创建时间
	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME;not null;default:CURRENT_TIMESTAMP;comment:'创建时间'" json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME;not null;default:CURRENT_TIMESTAMP;comment:'更新时间'" json:"updated_at"`
}

// TableName 设置WeappResourceLocationCrowdPack的表名为`weapp_resource_location_crowd_pack`
func (WeappResourceLocationCrowdPack) TableName() string {
	return "weapp_resource_location_crowd_pack"
}

type weappResourceLocationCrowdPackModelInterface interface {
	GetCrowdPacksByResourceLocationID(resourceLocationID int) (*WeappResourceLocationCrowdPack, error)
}

var WeappResourceLocationCrowdPackModelService weappResourceLocationCrowdPackModelInterface = &weappResourceLocationCrowdPackModelService{}

type weappResourceLocationCrowdPackModelService struct{}

// GetCrowdPacksByResourceLocationID 根据资源位ID获取关联的人群包
func (s *weappResourceLocationCrowdPackModelService) GetCrowdPacksByResourceLocationID(resourceLocationID int) (*WeappResourceLocationCrowdPack, error) {
	var resourceLocationCrowdPacks *WeappResourceLocationCrowdPack
	if err := mysql.MysqlDb.Where("without_id = ? AND state = 1", resourceLocationID).Find(&resourceLocationCrowdPacks).Error; err != nil {
		return nil, err
	}
	return resourceLocationCrowdPacks, nil
}
