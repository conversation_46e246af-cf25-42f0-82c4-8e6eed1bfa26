package dbModel

import (
	"errors"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"gorm.io/gorm"
	"time"
)

type WeappUserUnify struct {
	ID           int        `gorm:"primaryKey;autoIncrement"`
	AreaCode     int        `gorm:"not null;default:86"`
	Phone        string     `gorm:"size:50;not null;uniqueIndex:u_p_a,u_p;comment:手机号，若为注销用户，则为 手机号_id"`
	Gender       int8       `gorm:"not null;default:2;comment:1-男，2-女"`
	Nickname     string     `gorm:"size:50;default:'';comment:昵称"`
	Truename     string     `gorm:"size:50;default:'';comment:真实姓名"`
	From         int8       `gorm:"default:1;comment:注册渠道：1-app注册 2-后台添加 3-小程序 4-健萌圈 5-健萌注册 6-用户中心 7-h5登录注册"`
	RFrom        int8       `gorm:"not null;default:0;comment:转介渠道：0无渠道"`
	Birthday     *time.Time `gorm:"comment:生日"`
	Cdcard       string     `gorm:"size:32;default:''"`
	HeadImg      string     `gorm:"size:255;default:'';comment:头像"`
	Motto        string     `gorm:"size:128;default:'';comment:个性签名"`
	Province     string     `gorm:"size:32;comment:微信省份"`
	Country      string     `gorm:"size:64;comment:微信国家"`
	City         string     `gorm:"size:64;comment:微信城市"`
	Privilege    string     `gorm:"size:64;comment:微信特权"`
	Address      string     `gorm:"size:255;comment:地址"`
	UpdateTime   time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;autoUpdateTime;comment:更新时间"`
	CreateTime   time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;comment:添加时间"`
	Logout       int8       `gorm:"default:0;comment:1-注销账号 0-正常使用"`
	RemarkName   string     `gorm:"size:50;comment:备注名称"`
	UserType     int8       `gorm:"default:1;comment:用户类型 1 普通用户 2 水军用户"`
	VirtualPhone int8       `gorm:"default:1;comment:电话类型 1 正常电话号码 2虚拟电话号码"`
}

// TableName 设置WeappUserUnify的表名为`weapp_user_unify`
func (WeappUserUnify) TableName() string {
	return "weapp_user_unify"
}

type weappUserUnifyModelInterface interface {
	GetUserInfo(userId int) *WeappUserUnify
}

var WeappUserUnifyModelService weappUserUnifyModelInterface = &weappUserUnifyModelService{}

type weappUserUnifyModelService struct{}

// 获取用户信息
func (s *weappUserUnifyModelService) GetUserInfo(userId int) *WeappUserUnify {
	// 使用gorm获取用户信息
	var user WeappUserUnify
	if err := mysql.MysqlDb.Where("id = ?", userId).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录未找到，返回空的 WeappUserUnify 结构体
			return nil
		}
		// 其他错误，进行处理
		panic(err)
	}
	return &user
}
