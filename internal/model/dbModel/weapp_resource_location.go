package dbModel

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"time"
)

const (
	//0未投放 1投放中 2已废弃 3已停止
	STATUS_NO_PUT_ON = iota
	STATUS_PUT_ON
	STATUS_STOP
	STATUS_ABANDON
)

// WeappResourceLocation 资源位
type WeappResourceLocation struct {
	// Id ID
	Id int `gorm:"column:id;type:INT(11);not null;primaryKey;comment:'ID'" json:"id"`
	// Name 资源位名称
	Name string `gorm:"column:name;type:VARCHAR(50);default:NULL;comment:'资源位名称'" json:"name"`
	// Content 具体内容
	Content string `gorm:"column:content;type:VARCHAR(255);default:NULL;comment:'具体内容'" json:"content"`
	// ShowImg 展示图片
	ShowImg string `gorm:"column:show_img;type:VARCHAR(255);default:NULL;comment:'展示图片'" json:"show_img"`
	// AttachedImg 附属图片
	AttachedImg string `gorm:"column:attached_img;type:VARCHAR(255);default:NULL;comment:'附属图片'" json:"attached_img"`
	// LabId 标签ID
	LabId *int `gorm:"column:lab_id;type:INT(11);default:NULL;comment:'标签ID'" json:"lab_id"`
	// PositionType 资源定位类型:1-首页，2-社区，3-个人中心，4-我的档案，5-BMI详情，6-体重详情，7-餐卡点评，8-减脂营，9-记录饮食，10-超级菜谱，11-喝水提醒页，12-1.4.3版首页，13-UGC详情页，14-超级菜谱详情页，15-v1.5.6版首页，16-v1.5.9版首页 22：v1.8.7版本
	PositionType *int8 `gorm:"column:position_type;type:TINYINT(4);default:NULL;comment:'资源定位类型:1-首页，2-社区，3-个人中心，4-我的档案，5-BMI详情，6-体重详情，7-餐卡点评，8-减脂营，9-记录饮食，10-超级菜谱，11-喝水提醒页，12-1.4.3版首页，13-UGC详情页，14-超级菜谱详情页，15-v1.5.6版首页，16-v1.5.9版首页 22：v1.8.7版本'" json:"position_type"`
	// ResourceType 资源位展示类型：1-BANNER型，2-图文型，3-文字型，4-悬浮型，5-案例型，6-商品图文型，7-首页banner02，8-首页banner03，9-首页banner04，10-顶部banner   12-首页运营右上  13-首页运营右下  14-首页运营左边  17-首页开屏  18：首页活动tab  19：首页新人问卷贴片区-v200  20：首页吃出好身材贴片区-v200
	ResourceType *int8 `gorm:"column:resource_type;type:TINYINT(4);default:NULL;comment:'资源位展示类型：1-BANNER型，2-图文型，3-文字型，4-悬浮型，5-案例型，6-商品图文型，7-首页banner02，8-首页banner03，9-首页banner04，10-顶部banner   12-首页运营右上  13-首页运营右下  14-首页运营左边  17-首页开屏  18：首页活动tab  19：首页新人问卷贴片区-v200  20：首页吃出好身材贴片区-v200'" json:"resource_type"`
	// CrowdPackId 0-全部开放
	CrowdPackId *int `gorm:"column:crowd_pack_id;type:INT(11);default:NULL;comment:'0-全部开放'" json:"crowd_pack_id"`
	// Status 0未投放 1投放中 2已废弃 3已停止
	Status *int8 `gorm:"column:status;type:TINYINT(4);default:NULL;comment:'0未投放 1投放中 2已废弃 3已停止'" json:"status"`
	// Type 资源位类型 类型1：点评星级后资源位 类型2：点评标签资源位
	Type *int8 `gorm:"column:type;type:TINYINT(4);default:NULL;comment:'资源位类型 类型1：点评星级后资源位 类型2：点评标签资源位'" json:"type"`
	// LaunchPlatform 跳转平台1APP2小程序1,2小程序和APP
	LaunchPlatform string `gorm:"column:launch_platform;type:VARCHAR(50);default:NULL;comment:'跳转平台1APP2小程序1,2小程序和APP'" json:"launch_platform"`
	// ShowStime 展示开始时间
	ShowStime time.Time `gorm:"column:show_stime;type:DATETIME;default:NULL;comment:'展示开始时间'" json:"show_stime"`
	// ShowEtime 展示结束时间
	ShowEtime time.Time `gorm:"column:show_etime;type:DATETIME;default:NULL;comment:'展示结束时间'" json:"show_etime"`
	// AppLocationType 跳转配置 0 不做任何跳转 1 跳转webview 2跳转打卡 3跳转WE商城 4跳转小程序 7-跳转自研商城
	AppLocationType *int8 `gorm:"column:app_location_type;type:TINYINT(4);default:0;comment:'跳转配置 0 不做任何跳转 1 跳转webview 2跳转打卡 3跳转WE商城 4跳转小程序 7-跳转自研商城'" json:"app_location_type"`
	// AppConfigUrl APP配置-跳转URL
	AppConfigUrl string `gorm:"column:app_config_url;type:TEXT;default:NULL;comment:'APP配置-跳转URL'" json:"app_config_url"`
	// AppConfigTitle APP配置-跳转title
	AppConfigTitle string `gorm:"column:app_config_title;type:VARCHAR(255);default:NULL;comment:'APP配置-跳转title'" json:"app_config_title"`
	// AppConfigFoodItemType APP配置-跳转饮食打卡
	AppConfigFoodItemType *int8 `gorm:"column:app_config_food_item_type;type:TINYINT(4);default:NULL;comment:'APP配置-跳转饮食打卡'" json:"app_config_food_item_type"`
	// SmallappConfigUrl 小程序跳转路由
	SmallappConfigUrl string `gorm:"column:smallapp_config_url;type:TEXT;default:NULL;comment:'小程序跳转路由'" json:"smallapp_config_url"`
	// SmallappConfigTitle 小程序跳转路由名称
	SmallappConfigTitle string `gorm:"column:smallapp_config_title;type:VARCHAR(50);default:NULL;comment:'小程序跳转路由名称'" json:"smallapp_config_title"`
	// Weight 权重
	Weight *int `gorm:"column:weight;type:INT(11);default:0;comment:'权重'" json:"weight"`
	// UpdateTime 更新时间
	UpdateTime time.Time `gorm:"column:update_time;type:DATETIME;default:NULL;comment:'更新时间'" json:"update_time"`
	// AddUid 添加人UID
	AddUid *int `gorm:"column:add_uid;type:INT(11);default:NULL;comment:'添加人UID'" json:"add_uid"`
	// UpdateUid 更新UID
	UpdateUid *int `gorm:"column:update_uid;type:INT(11);default:NULL;comment:'更新UID'" json:"update_uid"`
	// AddTime 添加时间
	AddTime time.Time `gorm:"column:add_time;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'添加时间'" json:"add_time"`
	// CrowdPackVersion 1 老版本 2 新版本
	CrowdPackVersion int8 `gorm:"column:crowd_pack_version;type:TINYINT(4);not null;default:1;comment:'1 老版本 2 新版本'" json:"crowd_pack_version"`
	//新版本路由id
	AppRouterInfoID int `gorm:"column:app_router_info_id;type:int(11);default:0" json:"app_router_info_id"` // 新版本路由id
}

// TableName 设置WeappResourceLocation的表名为`weapp_resource_location`
func (WeappResourceLocation) TableName() string {
	return "weapp_resource_location"
}

type weappResourceLocationModelInterface interface {
	GetResourceLocationByID(id int) (*WeappResourceLocation, error)
	GetResourceLocationsByPositionAndType(positionType, resourceType int8) ([]*WeappResourceLocation, error)
	GetAllResourceLocations() ([]*WeappResourceLocation, error)
}

var WeappResourceLocationModelService weappResourceLocationModelInterface = &weappResourceLocationModelService{}

type weappResourceLocationModelService struct{}

// GetResourceLocationByID 根据ID获取资源位信息
func (s *weappResourceLocationModelService) GetResourceLocationByID(id int) (*WeappResourceLocation, error) {
	var resourceLocation WeappResourceLocation
	if err := mysql.MysqlDb.Where("id = ?", id).First(&resourceLocation).Error; err != nil {
		panic(err)
	}
	return &resourceLocation, nil
}

// GetResourceLocationsByPositionAndType 根据位置类型和资源类型获取资源位列表
func (s *weappResourceLocationModelService) GetResourceLocationsByPositionAndType(positionType, resourceType int8) ([]*WeappResourceLocation, error) {
	var resourceLocations []*WeappResourceLocation
	if err := mysql.MysqlDb.Debug().Where("position_type = ? AND resource_type = ? AND status = ?", positionType, resourceType, STATUS_PUT_ON).
		Order("weight DESC").Find(&resourceLocations).Error; err != nil {
		panic(err)
	}
	return resourceLocations, nil
}

// GetAllResourceLocations 获取所有资源位信息
func (s *weappResourceLocationModelService) GetAllResourceLocations() ([]*WeappResourceLocation, error) {
	var resourceLocations []*WeappResourceLocation
	if err := mysql.MysqlDb.Find(&resourceLocations).Where("status = ?", STATUS_PUT_ON).Error; err != nil {
		panic(err)
	}
	return resourceLocations, nil
}
