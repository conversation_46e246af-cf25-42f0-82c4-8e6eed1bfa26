package dbModel

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/db/mysql"
	"strings"
	"time"
)

// WeappCrowdPack 人群包
type WeappCrowdPack struct {
	Id int `gorm:"column:id;type:INT(11);not null;primaryKey" json:"id"`
	// Name 人群包名称
	Name string `gorm:"column:name;type:VARCHAR(50);default:NULL;comment:'人群包名称'" json:"name"`
	// Desc 人群包描述
	Desc string `gorm:"column:desc;type:VARCHAR(255);default:NULL;comment:'人群包描述'" json:"desc"`
	// DataCount 人群包数据量
	DataCount *int `gorm:"column:data_count;type:INT(11);default:NULL;comment:'人群包数据量'" json:"data_count"`
	// DataContent 人群包具体数据 json格式  保存手机号
	DataContent string `gorm:"column:data_content;type:TEXT;default:NULL;comment:'人群包具体数据 json格式  保存手机号'" json:"data_content"`
	// Status 人群包状态 0-待使用  1使用中 2已废弃
	Status  *int8     `gorm:"column:status;type:TINYINT(4);default:0;comment:'人群包状态 0-待使用  1使用中 2已废弃'" json:"status"`
	AddTime time.Time `gorm:"column:add_time;type:TIMESTAMP;default:CURRENT_TIMESTAMP" json:"add_time"`
	// AddUid 创建人uid
	AddUid     *int      `gorm:"column:add_uid;type:INT(11);default:NULL;comment:'创建人uid'" json:"add_uid"`
	UpdateTime time.Time `gorm:"column:update_time;type:TIMESTAMP;default:NULL" json:"update_time"`
	// UpdateUid 更新人UID
	UpdateUid *int `gorm:"column:update_uid;type:INT(11);default:NULL;comment:'更新人UID'" json:"update_uid"`
	// Type 人群包类型1 普通人群包 2定向人群包
	Type *int8 `gorm:"column:type;type:TINYINT(4);default:1;comment:'人群包类型1 普通人群包 2定向人群包'" json:"type"`
}

// TableName 设置WeappCrowdPack的表名为`weapp_crowd_pack`
func (WeappCrowdPack) TableName() string {
	return "weapp_crowd_pack"
}

type weappCrowdPackModelInterface interface {
	GetCrowdPackByID(id int) (*WeappCrowdPack, error)
	CheckPhoneInCrowdPack(crowdPackId int, phone string) (bool, error)
	GetAllCrowdPacks() ([]*WeappCrowdPack, error) // 新增方法
}

var WeappCrowdPackModelService weappCrowdPackModelInterface = &weappCrowdPackModelService{}

type weappCrowdPackModelService struct{}

// GetCrowdPackByID 根据ID获取人群包信息
func (s *weappCrowdPackModelService) GetCrowdPackByID(id int) (*WeappCrowdPack, error) {
	var crowdPack WeappCrowdPack
	if err := mysql.MysqlDb.Where("without_id = ?", id).First(&crowdPack).Error; err != nil {
		return nil, err
	}
	return &crowdPack, nil
}

// CheckPhoneInCrowdPack 检查手机号是否在人群包中
func (s *weappCrowdPackModelService) CheckPhoneInCrowdPack(crowdPackID int, phone string) (bool, error) {
	var crowdPack WeappCrowdPack
	if err := mysql.MysqlDb.Where("id = ?", crowdPackID).First(&crowdPack).Error; err != nil {
		return false, err
	}

	// 检查手机号是否在人群包中（以逗号分隔的手机号列表）
	phones := strings.Split(crowdPack.DataContent, ",")
	for _, p := range phones {
		if strings.TrimSpace(p) == phone {
			return true, nil
		}
	}

	return false, nil
}

// GetAllCrowdPacks 获取所有人群包
func (s *weappCrowdPackModelService) GetAllCrowdPacks() ([]*WeappCrowdPack, error) {
	var crowdPacks []*WeappCrowdPack
	if err := mysql.MysqlDb.Where("status = 1").Find(&crowdPacks).Error; err != nil {
		return nil, err
	}
	return crowdPacks, nil
}
