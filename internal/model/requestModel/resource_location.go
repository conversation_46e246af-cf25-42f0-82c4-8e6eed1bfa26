package requestModel

// ResourceLocationRequest 资源位请求参数
type ResourceLocationRequest struct {
	PositionType int8   `json:"position_type" binding:"required"`
	ResourceType int8   `json:"resource_type" binding:"required"`
	LabIds       string `json:"lab_ids"`
}

// RefreshResourceLocationCacheRequest 刷新资源位缓存请求参数
type RefreshResourceLocationCacheRequest struct {
	ID              int  `json:"id" binding:"required"`
	PositionType    int8 `json:"position_type" binding:"required"`
	ResourceType    int8 `json:"resource_type" binding:"required"`
	AppRouterInfoId int  `json:"app_router_info_id" binding:"required"`
}
