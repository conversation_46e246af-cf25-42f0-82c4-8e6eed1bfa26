package responseModel

// ResourceLocationResponse 资源位响应
type ResourceLocationResponse struct {
	ID                  int    `json:"id"`
	Name                string `json:"title"`
	Content             string `json:"content"`
	ShowImg             string `json:"img"`
	AttachedImg         string `json:"attached_img"`
	AppLocationType     int8   `json:"app_location_type"`
	AppConfigUrl        string `json:"app_config_url"`
	AppConfigTitle      string `json:"app_config_title"`
	SmallappConfigUrl   string `json:"smallapp_config_url"`
	SmallappConfigTitle string `json:"smallapp_config_title"`
	Router              Router `json:"router"`
	LabId               int    `json:"lab_id"`
}

type Router struct {
	NewPath string `json:"newPath"`
}

// ResourceLocationListResponse 资源位列表响应
type ResourceLocationListResponse []*ResourceLocationResponse
