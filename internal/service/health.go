package service

import (
	"context"
)

type IHealthService interface {
	Health(ctx context.Context) string
}

var HealthService IHealthService = &healthService{}

type healthService struct{}

func (s *healthService) Health(ctx context.Context) string {
	// 系统错误 alarm.DingDing("测试实施")
	// 自定义报警 alarm.CustomAlarm("自定义报警")
	// alarm.CustomAlarm("https://oapi.dingtalk.com/robot/send?access_token=a01da9fa6fa3bbad9812ae54bd9a21fe938b2dd502ad24090ff0e57162cdceb5", "dsadsa")
	return "OK"
}
