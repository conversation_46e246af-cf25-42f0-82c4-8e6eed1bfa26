package resourceLocation

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/utils"
	"github.com/syyongx/php2go"
	"strconv"
	"strings"
	"time"
	"wego-common-tmp/internal/cache/appRouterInfoCache"
	"wego-common-tmp/internal/cache/resourceLocationCache"
	"wego-common-tmp/internal/model/dbModel"
	"wego-common-tmp/internal/model/requestModel"
	"wego-common-tmp/internal/model/responseModel"
	"wego-common-tmp/internal/service/v1/appRouterService"
)

type IResourceLocationService interface {
	GetResourceLocations(ctx context.Context, ResourceLocationRequest requestModel.ResourceLocationRequest) ([]*responseModel.ResourceLocationResponse, error)
	RefreshResourceLocationCache(ctx context.Context, id int) error
	RefreshAllResourceLocationCache(ctx context.Context) error
	RefreshResourceLocationCacheByType(ctx context.Context, positionType, resourceType int8) error
}

var ResourceLocationService IResourceLocationService = &resourceLocationService{}

type resourceLocationService struct{}

// GetResourceLocations 获取资源位列表
func (s *resourceLocationService) GetResourceLocations(ctx context.Context, ResourceLocationRequest requestModel.ResourceLocationRequest) ([]*responseModel.ResourceLocationResponse, error) {
	// 获取资源位列表
	resourceLocations, err := resourceLocationCache.ResourceLocationService.GetResourceLocationsByPositionAndType(ctx, ResourceLocationRequest.PositionType, ResourceLocationRequest.ResourceType)

	if err != nil {
		return nil, err
	}

	var labIds []int
	if ResourceLocationRequest.LabIds != "" {
		// ResourceLocationRequest.LabIds的内容是以逗号分隔的字符串，例如："1,2,3"
		// 我们需要将其转换为一个切片，例如：[]int{"1", "2", "3"}
		labIdsStr := ResourceLocationRequest.LabIds
		labIdsSlice := strings.Split(labIdsStr, ",")

		for _, idStr := range labIdsSlice {
			id, err := strconv.Atoi(idStr)
			if err != nil {
				// 处理错误
				continue
			}
			labIds = append(labIds, id)
		}
	}

	// 过滤出对用户可见的资源位
	var result []*responseModel.ResourceLocationResponse
	for _, resourceLocation := range resourceLocations {
		// 先过滤时间范围失效的 资源位
		if resourceLocation.ShowStime.Unix() <= time.Now().Unix() && resourceLocation.ShowEtime.Unix() >= time.Now().Unix() {
			// 再过滤 labId 不匹配的 资源位
			if ResourceLocationRequest.LabIds != "" && resourceLocation.LabId != nil {
				// 判断 labIds 是否包含 resourceLocation.LabIds
				// resourceLocation.LabIds 是一个字符串，例如："1,2,3"
				if !utils.Contains(labIds, *resourceLocation.LabId) {
					continue
				}
			}
			if ResourceLocationRequest.LabIds == "" && resourceLocation.LabId != nil {
				continue
			}
			visible, err := resourceLocationCache.ResourceLocationService.IsResourceLocationVisible(ctx, resourceLocation)
			if err != nil {
				continue
			}

			if visible {
				result = append(result, convertToResourceLocationResponse(ctx, resourceLocation))
			}
		}
	}

	CountLimit := s.GetDataCountLimit(ResourceLocationRequest.ResourceType)
	// 保留result中前10条数据
	if len(result) > int(CountLimit) {
		result = result[:s.GetDataCountLimit(ResourceLocationRequest.ResourceType)]
	}

	return result, nil
}

// RefreshResourceLocationCache 刷新单个资源位缓存
func (s *resourceLocationService) RefreshResourceLocationCache(ctx context.Context, id int) error {
	return resourceLocationCache.ResourceLocationService.RefreshResourceLocationCache(ctx, id)
}

// RefreshAllResourceLocationCache 刷新所有资源位缓存
func (s *resourceLocationService) RefreshAllResourceLocationCache(ctx context.Context) error {
	return resourceLocationCache.ResourceLocationService.RefreshAllResourceLocationCache(ctx)
}

// RefreshResourceLocationCacheByType 根据位置类型和资源类型刷新缓存
func (s *resourceLocationService) RefreshResourceLocationCacheByType(ctx context.Context, positionType, resourceType int8) error {
	return resourceLocationCache.ResourceLocationService.RefreshResourceLocationCacheByType(ctx, positionType, resourceType)
}

// convertToResourceLocationResponse 将数据库模型转换为响应模型
func convertToResourceLocationResponse(ctx context.Context, resourceLocation *dbModel.WeappResourceLocation) *responseModel.ResourceLocationResponse {
	var tmpLabId int
	if resourceLocation.LabId == nil {
		tmpLabId = 0
	} else {
		tmpLabId = *resourceLocation.LabId
	}
	SmallappConfigUrl, err := php2go.URLDecode(resourceLocation.SmallappConfigUrl)
	if err != nil {
		panic(err)
	}
	resp := &responseModel.ResourceLocationResponse{
		ID:                  resourceLocation.Id,
		Name:                resourceLocation.Name,
		Content:             resourceLocation.Content,
		ShowImg:             resourceLocation.ShowImg,
		AttachedImg:         resourceLocation.AttachedImg,
		SmallappConfigUrl:   SmallappConfigUrl,
		SmallappConfigTitle: resourceLocation.SmallappConfigTitle,
		LabId:               tmpLabId,
	}

	if resourceLocation.AppLocationType != nil {
		resp.AppLocationType = *resourceLocation.AppLocationType
	}
	if resourceLocation.AppRouterInfoID > 0 {
		// 获取最新的路由信息（使用二级缓存）
		routerInfo, err := appRouterInfoCache.AppRouterInfoService.GetAppRouterInfoByID(ctx, resourceLocation.AppRouterInfoID)
		if err != nil {
			return resp
		}
		if routerInfo != nil {
			resp.Router.NewPath = routerInfo.Router
		}
	} else {
		resp.AppConfigUrl = resourceLocation.AppConfigUrl
		resp.AppConfigTitle = resourceLocation.AppConfigTitle

		resp.Router.NewPath = appRouterService.GetCompatiblePath(int(resp.AppLocationType), resp.AppConfigUrl, resp.AppConfigTitle)
	}
	return resp
}

// GetDataCountLimit 根据资源类型获取展示数量限制
func (s *resourceLocationService) GetDataCountLimit(resourceType int8) int8 {
	// 使用映射存储资源类型到限制数量的对应关系
	limits := map[int8]int8{
		1:  5,
		4:  1,
		5:  5,
		3:  1,
		2:  10,
		6:  10,
		7:  3,
		8:  1,
		9:  3,
		10: 1,
		11: 1,
		12: 5,
		13: 5,
		14: 1,
		15: 1,
		16: 1,
		30: 3,
	}

	// 如果资源类型存在于映射中，返回对应值；否则返回默认值 3
	if limit, exists := limits[resourceType]; exists {
		return limit
	}

	return 3
}
