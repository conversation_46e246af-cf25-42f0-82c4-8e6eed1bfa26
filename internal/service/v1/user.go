package v1

import (
	"context"
	"wego-common-tmp/internal/cache/userCache"
	"wego-common-tmp/internal/model/dbModel"
)

type IUserService interface {
	UserInfo(ctx context.Context, unifyId int) dbModel.WeappUserUnify
}

var UserService IUserService = &userService{}

type userService struct{}

func (s *userService) UserInfo(ctx context.Context, unifyId int) dbModel.WeappUserUnify {
	userUnfiy := userCache.UserService.GetUserInfo(ctx, unifyId)
	return userUnfiy
}
