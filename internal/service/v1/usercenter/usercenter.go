package usercenter

import (
	"context"
	"strconv"

	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/datacenter"
)

type IUserCenterService interface {
	// GetUcenterUIDByUnifyID 根据统一ID获取用户中心UID
	GetUcenterUIDByUnifyID(ctx context.Context, unifyId int) (int64, error)
}

var UserCenterService IUserCenterService = &userCenterService{}

type userCenterService struct{}

// GetUcenterUIDByUnifyID 根据统一ID获取用户中心UID
func (s *userCenterService) GetUcenterUIDByUnifyID(ctx context.Context, unifyId int) (int64, error) {
	// 构建请求参数
	req := &datacenter.GetCenterUIDByBizUIDsReq{
		FromSource: datacenter.SourceBTW,
		UIDs:       []int64{int64(unifyId)},
		ToSource:   datacenter.SourceBTW,
	}

	// 调用远程服务
	resp, err := base.RemoteServiceMgr.Call(ctx, consts.UserCenterService, consts.GetCenterUserBySourceUIDs, req)
	if err != nil {
		return 0, err
	}

	// 类型断言
	result, ok := resp.(*datacenter.Resp[map[string]*datacenter.CenterUser])
	if !ok || result == nil || result.Data == nil {
		return 0, nil
	}

	// 获取用户中心UID
	userData, exists := result.Data[strconv.Itoa(unifyId)]
	if !exists || userData == nil {
		return 0, nil
	}

	return userData.ID, nil
}
