package tagcenter

import (
	"context"
	"gitee.com/jianmengkeji/wego-common-pkg/src/consts"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/datacenter"
	"gitee.com/jianmengkeji/wego-common-pkg/src/remote/javacenter"
	"strconv"
)

type ITagCenterService interface {
	// IsUserInPackage 检查用户是否在指定的包列表中
	IsUserInPackage(ctx context.Context, packageIDList []int, ucenterUID int64) string
}

var TagCenterService ITagCenterService = &tagCenterService{}

type tagCenterService struct{}

// IsUserInPackage 检查用户是否在指定的包列表中
func (s *tagCenterService) IsUserInPackage(ctx context.Context, packageIDList []int, ucenterUID int64) string {
	// 使用缓存服务获取结果
	// 如果缓存不存在，则调用远程服务获取结果
	req := &datacenter.UidIsInPackage{
		BusinessLine:  1,
		PackageIDList: packageIDList,
		UcenterID:     ucenterUID,
	}

	// 创建新的上下文，避免使用原始上下文可能导致的问题
	newCtx := context.WithValue(context.Background(), consts.TRACE_ID_KEY, "")
	resp, err := base.RemoteServiceMgr.Call(newCtx, consts.JavaCenterService, consts.UidIsInPackage, req)
	if err != nil {
		return "false"
	}

	// 类型断言
	result, ok := resp.(*javacenter.Resp[bool])
	if !ok || result == nil {
		return "false"
	}

	// 返回结果
	return strconv.FormatBool(result.Data)
}
