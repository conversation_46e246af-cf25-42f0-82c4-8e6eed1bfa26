package appRouterService

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/helper"
	"net/url"
	"strings"

	"wego-common-tmp/global"
)

// AppRouterConfig 应用路由配置服务
type AppRouterConfig struct{}

// 路由类型常量
const (
	AppLocationWebview      = 1  // 跳转webview
	AppLocationPclock       = 2  // 跳转打卡界面
	AppLocationWeshop       = 3  // 跳转微商城
	AppLocationSmallapp     = 4  // 跳转小程序
	AppLocationAppinside    = 5  // 跳转APP内部
	AppLocationJianmeng     = 6  // 跳转APP内部健萌webview
	AppLocationBetterweH5   = 7  // 跳转betterBE H5
	AppLocationWebviewV2    = 8  // 跳转betterBE H5 V2
	AppLocationWeshopV2     = 11 // 跳转微商城V2
	AppLocationSmallappV2   = 9  // 跳转小程序 V2
	AppLocationEcshop       = 12 // 自研商城（排除商品鉴权，仅仅只是跳转到自研商城页面）
	AppLocationEcshopGoods  = 13 // 自研商城（跳转指定商品详情页面）
	AppLocationJianmengh5   = 14 // 健萌H5链接
	AppLocationJianmengh5V1 = 15 // 健萌H5链接旧版
)

// 跳转小程序的username参数
const LocationSmallappParamUsername = "gh_0d804ee2c21b"

// 路由参数结构
type RouterParam struct {
	AppConfigURL          string `json:"app_config_url"`
	AppConfigTitle        string `json:"app_config_title"`
	AppConfigFoodItemType int    `json:"app_config_food_item_type"`
	EcshopGoodsID         int    `json:"ecshop_goods_id"`
	EcshopShopID          int    `json:"ecshop_shop_id"`
}

// 路由参数项结构
type ParamItem struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

// 路由配置结构
type RouterConfig struct {
	Params  []ParamItem `json:"params,omitempty"`
	Path    string      `json:"path,omitempty"`
	NewPath string      `json:"newPath,omitempty"`
}

// GetRouterConfig 获取指定跳转路由
// 参数介绍：
//
//	appLocationType：路由类型（参考上面常量配置）
//	param：路由参数
//	isEncode：是否需要URL编码
//
// 返回：路由配置（可能是map或string类型）
func (a *AppRouterConfig) GetRouterConfig(appLocationType int, param RouterParam, isEncode bool) string {
	switch appLocationType {
	case AppLocationWebview:
		str := a.getWebViewConfigV2(param.AppConfigURL, param.AppConfigTitle, isEncode)
		return str
	case AppLocationPclock:
		return a.getTabConfig(param.AppConfigFoodItemType)
	case AppLocationWeshop:
		return a.getWeShopV2(param.AppConfigURL, param.AppConfigTitle)
	case AppLocationSmallapp:
		return a.getSmallAppV2(param.AppConfigURL, param.AppConfigTitle, isEncode)
	case AppLocationAppinside:
		return a.getLocationAppInside(param.AppConfigURL, param.AppConfigTitle)
	case AppLocationJianmeng:
		return a.getJMWebViewConfig(param.AppConfigURL, param.AppConfigTitle)
	case AppLocationBetterweH5:
		return a.betterWeH5RouterChange(param.AppConfigURL, param.AppConfigTitle, isEncode)
	case AppLocationWebviewV2:
		return a.getWebViewConfigV2(param.AppConfigURL, param.AppConfigTitle, isEncode)
	case AppLocationWeshopV2:
		return a.getWeShopV2(param.AppConfigURL, param.AppConfigTitle)
	case AppLocationSmallappV2:
		return a.getSmallAppV2(param.AppConfigURL, param.AppConfigTitle, isEncode)
	case AppLocationEcshop:
		return a.getEcshopURL(param.AppConfigURL, param.AppConfigTitle, isEncode)
	case AppLocationEcshopGoods:
		return a.getEcshopURLByGoodsID(param.EcshopGoodsID, param.EcshopShopID, param.AppConfigTitle)
	case AppLocationJianmengh5:
		return a.getWebJianMengConfig(param.AppConfigURL, param.AppConfigTitle, true)
	case AppLocationJianmengh5V1:
		return a.getGymWebView(param.AppConfigURL, param.AppConfigTitle)
	}
	return ""
}

// getWebViewConfig 组装跳转webview的配置
func (a *AppRouterConfig) getWebViewConfig(urlStr, title string) interface{} {
	if urlStr == "" {
		return nil
	}

	return RouterConfig{
		Params: []ParamItem{
			{Key: "url", Value: urlStr},
			{Key: "title", Value: title},
		},
		Path: "/web",
	}
}

// getGymWebView 组装跳转健身webview的配置
func (a *AppRouterConfig) getGymWebView(urlStr, title string) string {
	if urlStr == "" {
		return ""
	}

	return "/web?url=" + urlStr + "&title=" + title + "&isJM=1&isAddToken=false"
}

// getTabConfig 组装跳转打卡界面的配置
func (a *AppRouterConfig) getTabConfig(homeType int) string {
	return "/home?pageTag=3"
}

// getWeShop 组装跳转WE商城配置
func (a *AppRouterConfig) getWeShop(urlStr, title string) string {
	return "/wmOtherPage?url=" + urlStr + "&title=" + title + "&fromType=11"
}

// getWeShopV2 组装跳转WE商城配置V2
func (a *AppRouterConfig) getWeShopV2(urlStr, title string) string {
	return "/wmOtherPage?url=" + urlStr + "&title=" + title + "&fromType=11"
}

// getLocationAppInside 组装跳转APP内部
func (a *AppRouterConfig) getLocationAppInside(urlStr, title string) string {
	if urlStr == "" {
		return ""
	}
	return urlStr
}

// getJMWebViewConfig 组装跳转健萌的webview的配置
func (a *AppRouterConfig) getJMWebViewConfig(urlStr, title string) string {
	if urlStr == "" {
		return ""
	}
	return "/jMAppointment?url=" + urlStr + "&title=" + title
}

// getWebViewConfigV2 组装跳转web view的配置V2
func (a *AppRouterConfig) getWebViewConfigV2(urlStr, title string, isEncode bool) string {
	var str string
	if strings.Contains(urlStr, "?") {
		str = "&"
	} else {
		str = "?"
	}

	if isEncode {
		return "/web?url=" + url.QueryEscape(urlStr) + str + "title=" + url.QueryEscape(title)
	}
	return "/web?url=" + urlStr + str + "title=" + title
}

// getWebJianMengConfig 组装跳转健盟H5
func (a *AppRouterConfig) getWebJianMengConfig(urlStr, title string, isEncode bool) string {
	var str string
	if strings.Contains(urlStr, "?") {
		str = "&"
	} else {
		str = "?"
	}

	if isEncode {
		return "/web?url=" + url.QueryEscape(urlStr) + str + "title=" + url.QueryEscape(title) + "&isJM=1&isAddToken=false"
	}
	return "/web?url=" + urlStr + str + "title=" + title + "&isJM=1&isAddToken=false"
}

// betterWeH5RouterChange h5路由转换 针对APP跳转自己的H5 /web
func (a *AppRouterConfig) betterWeH5RouterChange(h5router, title string, isEncode bool) string {
	if h5router == "" {
		return ""
	}

	domain := global.AppConfig.BaseConfig.BetterWeH5URL

	if isEncode {
		return "/web?url=" + url.QueryEscape(domain+h5router+"&title="+title) + "&title=" + url.QueryEscape(title)
	}
	return "/web?url=" + domain + h5router + "&title=" + title
}

// getSmallAppV2 组装跳转小程序的配置V2
func (a *AppRouterConfig) getSmallAppV2(path, userName string, isEncode bool) string {
	if isEncode {
		return "/miniProgram?userName=" + userName + "&path=" + url.QueryEscape(path)
	}
	return "/miniProgram?userName=" + userName + "&path=" + path
}

// GetFoodRouterByID 根据食物ID获取食物跳转
func (a *AppRouterConfig) GetFoodRouterByID(id int, isSuper int) string {
	domain := global.AppConfig.BaseConfig.BetterWeH5URL
	var urlStr string

	if isSuper != 0 {
		urlStr = "/web?url=" + url.QueryEscape(domain+"/calorie-query/super-food-detail?id="+helper.AnyToString(id))
	} else {
		urlStr = "/web?url=" + url.QueryEscape(domain+"/calorie-query/food-info?foodid="+helper.AnyToString(id))
	}

	return urlStr
}

// GetExpertByID 达人跳转
func (a *AppRouterConfig) GetExpertByID(id int) string {
	return "/starDetails?id=" + helper.AnyToString(id)
}

// GetRecipeByID 菜谱跳转
func (a *AppRouterConfig) GetRecipeByID(id int) string {
	return "/recipeDetails?id=" + helper.AnyToString(id)
}

// GetTopicByID 话题跳转
func (a *AppRouterConfig) GetTopicByID(id int, topicName string) string {
	return "/ugcTopicUgcListPage?topicId=" + helper.AnyToString(id) + "&topicName=" + topicName
}

// GetUgcByID ugc详情跳转
func (a *AppRouterConfig) GetUgcByID(id int) string {
	return "/ugcDetails?id=" + helper.AnyToString(id) + "&tabType=weapp_group_unify"
}

// getEcshopURL 跳转自研商城
func (a *AppRouterConfig) getEcshopURL(h5router, title string, isEncode bool) string {
	if h5router == "" {
		return ""
	}

	echost := global.AppConfig.BaseConfig.EcshopH5Host

	if isEncode {
		return "/ecShop?url=" + url.QueryEscape(echost+h5router+"&title="+title)
	}
	return "/ecShop?url=" + echost + h5router + "&title=" + title
}

// getEcshopURLByGoodsID 通过商品ID获取跳转自研商城链接
// 【
// 2024-04-02 之前版本：共用 const APP_LOCATION_ECSHOP = 12; // 自研商城（排除商品鉴权）
// 通过代码逻辑来跳转不同的自研商城
// 2024-04-02 之后采用 APP_LOCATION_ECSHOP_GOODS = 13; // 自研商城（跳转指定商品详情页面） 跳转自研商城商品详情页
// 】
func (a *AppRouterConfig) getEcshopURLByGoodsID(goodsID, shopID int, title string, action ...string) string {
	actionPath := "/pages/item/espier-detail"
	if len(action) > 0 && action[0] != "" {
		actionPath = action[0]
	}

	if actionPath == "" || goodsID == 0 {
		return ""
	}

	ecHost := global.AppConfig.BaseConfig.EcshopH5Host
	urlStr := ecHost + actionPath + "?" + "id=" + helper.AnyToString(goodsID) + "&dtid=" + helper.AnyToString(shopID)

	// 默认参数
	shouldGetToken := true
	isEncode := true

	// 可选参数处理
	if len(action) > 1 {
		shouldGetToken = action[1] == "true"
	}
	if len(action) > 2 {
		isEncode = action[2] == "true"
	}

	if isEncode {
		urlStr = url.QueryEscape(urlStr)
	}

	if shouldGetToken {
		urlStr += "&isGetShopToken=1&isLogin=1"
	}

	if title != "" {
		urlStr += "&title=" + title
	}

	return "/ecShop?url=" + urlStr
}

// UgcRouter UGC整体路由跳转
func UgcRouter(versionType int) string {
	if versionType == 2 {
		return "/superPunchInDetails"
	}
	return "/ugcDetails"
}

// ExpertRouter 达人详情跳转
func ExpertRouter(expertID int) string {
	return "/starDetails?id=" + helper.AnyToString(expertID)
}

// UserRouter 用户个人主页路由
func UserRouter(uid int) string {
	return "/userDetailsPage?id=" + helper.AnyToString(uid)
}

// GetDietitianRoute 获取营养师详情路由
func GetDietitianRoute(expertDietitianID int) string {
	return "/dietitianDetails?id=" + helper.AnyToString(expertDietitianID)
}

// GetCompatiblePath 兼容以前新旧APP版本关于newPath的跳转路径
func GetCompatiblePath(jumpType int, jumpURL, jumpName string) string {
	appRouter := &AppRouterConfig{}
	var jumpReturnURL string

	param := RouterParam{
		AppConfigURL:   jumpURL,
		AppConfigTitle: jumpName,
	}

	switch jumpType {
	case AppLocationAppinside:
		jumpReturnURL = appRouter.GetRouterConfig(jumpType, param, false)
	case AppLocationWebviewV2, AppLocationJianmengh5:
		jumpReturnURL = appRouter.GetRouterConfig(jumpType, param, true)
	default:
		param.AppConfigURL = url.QueryEscape(jumpURL)
		jumpReturnURL = appRouter.GetRouterConfig(jumpType, param, false)
	}

	return jumpReturnURL
}

// AppRouterConfigService 应用路由配置服务实例
var AppRouterConfigService = &AppRouterConfig{}
