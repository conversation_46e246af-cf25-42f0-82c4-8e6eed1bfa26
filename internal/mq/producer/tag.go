package producer

import (
	"context"
	mqModel "wego-common-tmp/internal/model/mqModel"
	"wego-common-tmp/internal/mq"
)

func SendTagChangeMsg(ctx context.Context) error {
	var msg mqModel.TestMsg
	msg.Text = "test"
	return mq.TestProducer.Send(ctx, &msg)
}

func needSendMsg(ctx context.Context) bool {
	// 同步逻辑已确认废弃，直接返回false，代码保留
	return false
	//if app.IsPrdEnv() {
	//	return true
	//}
	//var syncTag string
	//switch tp := ctx.(type) {
	//case *app.Context:
	//	syncTag = tp.GetSyncTag()
	//}
	//return syncTag == "true"
}
