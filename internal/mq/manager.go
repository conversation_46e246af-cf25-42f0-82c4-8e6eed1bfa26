package mq

import (
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/log"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/mq/base"
	"gitee.com/jianmengkeji/wego-common-pkg/src/pkg/mq/rabbitmq"
	amqp "github.com/rabbitmq/amqp091-go"
	"go.uber.org/zap"
	"wego-common-tmp/global"
	"wego-common-tmp/internal/mq/consumer"
)

var TestProducer base.IProducer

func Init(manager base.IManager) error {
	registerQueueErr := registerQueue(manager)
	if registerQueueErr != nil {
		return registerQueueErr
	}

	registerProducerErr := registerProducer(manager)
	if registerProducerErr != nil {
		return registerProducerErr
	}
	registerConsumerErr := registerConsumer(manager)
	if registerConsumerErr != nil {
		return registerConsumerErr
	}
	return nil
}

// 注册消费者
const (
	TestQueueName = base.QueueName("test_queue")
)

func registerConsumer(manager base.IManager) error {
	err := manager.RegisterConsumer(TestQueueName, rabbitmq.NewConsumerBuilder().
		WithHandler(consumer.TestConsumer.Consumer).
		WithAutoAck(false).
		WithShareChannel(false).
		WithNum(1).
		Build())
	if err != nil {
		log.AppLogger.Error("register consumer failed", zap.Error(err), zap.String("queue", TestQueueName.String()))
		return err
	}
	return nil
}

var (
	ProducerNameTagChange = base.ProducerName("test_queue")
)

func registerProducer(manager base.IManager) error {
	err := manager.RegisterProducer(TestQueueName,
		rabbitmq.NewProducerBuilder().
			WithName(ProducerNameTagChange).
			WithDeliveryMode(amqp.Transient).
			WithAutoConfirm(false).
			Build())
	if err != nil {
		log.AppLogger.Error("register producer failed", zap.Error(err), zap.String("producer", ProducerNameTagChange.String()))
		return err
	}
	TestProducer = manager.GetProducer(ProducerNameTagChange)

	return nil
}

func registerQueue(manager base.IManager) error {
	queue := TestQueueName
	exchange := rabbitmq.NewExchangeBuilder().
		WithName(global.DefaultExchange).
		WithDurable(true).
		WithType(amqp.ExchangeDirect).
		Build()
	registerQueueErr := manager.RegisterQueue(queue, rabbitmq.WithExchangeOption(queue, exchange), rabbitmq.WithQosOption(1))
	if registerQueueErr != nil {
		log.AppLogger.Error("register queue failed", zap.Error(registerQueueErr), zap.String("queue", queue.String()))
		return registerQueueErr
	}
	return nil
}

var RabbitMQ base.IManager
