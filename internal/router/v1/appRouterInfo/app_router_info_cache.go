package appRouterInfo

import (
	"github.com/gin-gonic/gin"
	"wego-common-tmp/internal/api/v1/appRouterInfo"
)

// SetupAppRouterInfoCacheRoutes 设置app路由信息缓存相关路由
func SetupAppRouterInfoCacheRoutes(router *gin.RouterGroup) {
	appRouterInfoCacheGroup := router.Group("/appRouterInfoCache")
	{
		// 初始化所有app路由信息缓存
		appRouterInfoCacheGroup.POST("/init", appRouterInfo.AppRouterInfoCacheController.InitAppRouterInfoCache)

		// 刷新指定app路由信息缓存
		appRouterInfoCacheGroup.POST("/refresh", appRouterInfo.AppRouterInfoCacheController.RefreshAppRouterInfoCache)
	}
}
